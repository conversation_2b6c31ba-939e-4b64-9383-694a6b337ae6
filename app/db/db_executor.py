import psycopg
from psycopg.sql import SQL, Composed
from psycopg import OperationalError, InterfaceError, IntegrityError

from psycopg.pq import TransactionStatus
from typing import Any, Optional, Union, cast, Callable, LiteralString
import logging
import time
import threading
from contextlib import contextmanager
from functools import wraps
from dataclasses import dataclass
from enum import Enum


class ConnectionState(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"


@dataclass
class RetryConfig:
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 30.0
    backoff_multiplier: float = 2.0
    jitter: bool = True


@dataclass
class DatabaseMetrics:
    total_queries: int = 0
    successful_queries: int = 0
    failed_queries: int = 0
    retry_attempts: int = 0
    connection_failures: int = 0

    @property
    def success_rate(self) -> float:
        return (self.successful_queries / self.total_queries * 100) if self.total_queries > 0 else 0.0


class DatabaseError(Exception):
    """Custom database error with enhanced context"""
    def __init__(self, message: str, original_error: Optional[Exception] = None, query: Optional[str] = None, params: Any = None):
        super().__init__(message)
        self.original_error = original_error
        self.query = query
        self.params = params
        self.timestamp = time.time()


class ConnectionPoolExhaustedError(DatabaseError):
    """Raised when connection pool is exhausted"""
    pass


class QueryTimeoutError(DatabaseError):
    """Raised when query execution times out"""
    pass


class DatabaseExecutor:
    def __init__(self, db_name, db_user, db_password, db_host, db_port,
                 logger: Optional[logging.Logger] = None,
                 retry_config: Optional[RetryConfig] = None,
                 connection_timeout: int = 30,
                 query_timeout: int = 300,
                 health_check_interval: int = 60):

        self.db_params = {
            'dbname': db_name,
            'user': db_user,
            'password': db_password,
            'host': db_host,
            'port': db_port,
            'connect_timeout': connection_timeout
        }

        # Setup logging
        self.logger = logger or self._setup_default_logger()

        # Configuration
        self.retry_config = retry_config or RetryConfig()
        self.query_timeout = query_timeout
        self.health_check_interval = health_check_interval

        # State management
        self.connection_state = ConnectionState.HEALTHY
        self.metrics = DatabaseMetrics()
        self._connection_lock = threading.RLock()
        self._last_health_check = 0

        # Initialize connection
        self.connection = None
        self._initialize_connection()

    def _setup_default_logger(self) -> logging.Logger:
        """Setup default logger if none provided"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _initialize_connection(self):
        """Initialize database connection with error handling"""
        try:
            self.connection = self.get_connection()
            self.connection_state = ConnectionState.HEALTHY
        except Exception as e:
            self.connection_state = ConnectionState.FAILED
            self.metrics.connection_failures += 1
            self.logger.error(f"Failed to initialize database connection: {e}")
            raise DatabaseError("Failed to initialize database connection", e) from e

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt with exponential backoff and jitter"""
        delay = min(
            self.retry_config.base_delay * (self.retry_config.backoff_multiplier ** attempt),
            self.retry_config.max_delay
        )

        if self.retry_config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter

        return delay

    def _is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error is retryable"""
        retryable_errors = (
            OperationalError,
            InterfaceError,
            ConnectionError,
            TimeoutError
        )

        if isinstance(error, retryable_errors):
            return True

        # Check for specific error messages that indicate transient issues
        error_msg = str(error).lower()
        transient_indicators = [
            'connection', 'timeout', 'network', 'temporary', 'busy', 'locked',
            'transaction is aborted'  # Failed transaction state is retryable
        ]

        return any(indicator in error_msg for indicator in transient_indicators)

    def _handle_connection_error(self, error: Exception):
        """Handle connection-related errors"""
        self.connection_state = ConnectionState.DEGRADED
        self.metrics.connection_failures += 1
        self.logger.warning(f"Connection error detected: {error}")

        # Check if it's a failed transaction error
        if "transaction is aborted" in str(error).lower():
            pass  # Will create fresh connection below

        # Attempt to close and recreate connection
        try:
            if self.connection:
                self.connection.close()
        except Exception:
            pass
        finally:
            self.connection = None

    def _perform_health_check(self) -> bool:
        """Perform database health check"""
        current_time = time.time()
        if current_time - self._last_health_check < self.health_check_interval:
            return self.connection_state == ConnectionState.HEALTHY

        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

            self.connection_state = ConnectionState.HEALTHY
            self._last_health_check = current_time
            return True

        except Exception as e:
            self.connection_state = ConnectionState.FAILED
            self.logger.warning(f"Health check failed: {e}")
            return False

    @staticmethod
    def retry_on_failure(operation_name: str):
        """Decorator for retry logic"""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(self, *args, **kwargs):
                last_exception = None

                for attempt in range(self.retry_config.max_attempts):
                    try:
                        self.metrics.total_queries += 1
                        result = func(self, *args, **kwargs)
                        self.metrics.successful_queries += 1

                        return result

                    except Exception as e:
                        last_exception = e
                        self.metrics.failed_queries += 1

                        if attempt < self.retry_config.max_attempts - 1:
                            if self._is_retryable_error(e):
                                self.metrics.retry_attempts += 1
                                delay = self._calculate_retry_delay(attempt)

                                self.logger.warning(
                                    f"{operation_name} failed (attempt {attempt + 1}/{self.retry_config.max_attempts}): {e}. "
                                    f"Retrying in {delay:.2f} seconds..."
                                )

                                # Handle connection errors
                                if isinstance(e, (OperationalError, InterfaceError)):
                                    self._handle_connection_error(e)

                                time.sleep(delay)
                                continue
                            else:
                                # Non-retryable error, break immediately
                                self.logger.error(f"{operation_name} failed with non-retryable error: {e}")
                                break
                        else:
                            self.logger.error(f"{operation_name} failed after {self.retry_config.max_attempts} attempts")

                # All retries exhausted or non-retryable error
                raise DatabaseError(
                    f"{operation_name} failed after {self.retry_config.max_attempts} attempts",
                    last_exception,
                    kwargs.get('query') or (args[0] if len(args) > 0 else None),
                    kwargs.get('params') or (args[1] if len(args) > 1 else None)
                )

            return wrapper
        return decorator

    @contextmanager
    def _get_cursor(self):
        """Context manager for database cursor with timeout handling"""
        conn = None
        cursor = None
        start_time = time.time()

        try:
            # Health check before critical operations
            if not self._perform_health_check():
                raise DatabaseError("Database health check failed")

            conn = self.get_connection()
            cursor = conn.cursor()

            # Check if connection is in a failed transaction state
            try:
                if conn.info.transaction_status != TransactionStatus.IDLE:
                    self.logger.warning("Connection in failed transaction state, rolling back")
                    try:
                        conn.rollback()
                    except Exception as rollback_error:
                        self.logger.error(f"Failed to rollback transaction: {rollback_error}")
                        # Close and get a new connection
                        conn.close()
                        conn = self.get_connection()
                        cursor = conn.cursor()
            except AttributeError:
                # Fallback for older psycopg versions that might not have transaction_status
                pass

            # Set statement timeout if supported
            try:
                timeout_query = SQL("SET statement_timeout = {timeout}").format(
                    timeout=self.query_timeout * 1000
                )
                cursor.execute(timeout_query)
            except Exception:
                pass  # Not all PostgreSQL versions support this

            yield conn, cursor

        except Exception as e:
            execution_time = time.time() - start_time

            # Handle failed transaction state
            if conn:
                try:
                    # Always rollback on error to clean up transaction state
                    if hasattr(conn, 'info') and hasattr(conn.info, 'transaction_status'):
                        if conn.info.transaction_status != TransactionStatus.IDLE:
                            conn.rollback()
                    else:
                        # Fallback: always try to rollback on error
                        conn.rollback()
                except Exception as rollback_error:
                    self.logger.warning(f"Failed to rollback after error: {rollback_error}")

            if execution_time > self.query_timeout:
                raise QueryTimeoutError(f"Query execution timed out after {execution_time:.2f} seconds", e) from e
            raise
        finally:
            if cursor:
                try:
                    cursor.close()
                except Exception as e:
                    self.logger.warning(f"Error closing cursor: {e}")
            if conn:
                try:
                    conn.close()
                except Exception as e:
                    self.logger.warning(f"Error closing connection: {e}")

    def get_connection(self):
        """Create and return a new database connection with connection pooling awareness"""
        with self._connection_lock:
            try:
                return psycopg.connect(**self.db_params)
            except Exception as e:
                self.logger.error(f"Failed to create database connection: {e}")
                raise DatabaseError("Failed to create database connection", e) from e

    @retry_on_failure("SELECT query")
    def execute_select(self, query: Union[str, SQL, Composed], params: tuple[Any, ...] = (),
                      float_columns: Optional[list[int]] = None) -> list[tuple]:
        """
        Execute a SELECT statement and return the results, with specified columns cast to floats.
        Enhanced with comprehensive error handling and logging.
        """
        start_time = time.time()

        try:
            with self._get_cursor() as (_conn, cursor):
                # Convert string query to SQL object if needed
                if isinstance(query, str):
                    query = SQL(cast(LiteralString, query))

                self.logger.debug(f"Executing SELECT query: {query}")
                cursor.execute(query, params)
                results = cursor.fetchall()

                # Convert specified columns to float
                if float_columns:
                    converted_results = []
                    for row_idx, row in enumerate(results):
                        converted_row = list(row)
                        for col_index in float_columns:
                            if col_index < len(converted_row):
                                try:
                                    if converted_row[col_index] is not None:
                                        converted_row[col_index] = float(converted_row[col_index])
                                except (ValueError, TypeError) as e:
                                    self.logger.warning(
                                        f"Failed to convert value at row {row_idx}, column {col_index} to float: "
                                        f"{converted_row[col_index]} - {e}"
                                    )
                                    converted_row[col_index] = None
                            else:
                                self.logger.warning(f"Column index {col_index} out of range for row {row_idx}")
                        converted_results.append(tuple(converted_row))
                    results = converted_results

                execution_time = time.time() - start_time
                return results

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"SELECT query failed after {execution_time:.3f}s: {e}")
            raise

    @retry_on_failure("UPDATE query")
    def execute_update(self, query: Union[str, SQL, Composed], params: tuple[Any, ...] = ()) -> int:
        """
        Execute an UPDATE statement and return the number of affected rows.
        Enhanced with comprehensive error handling and logging.
        """
        start_time = time.time()

        try:
            with self._get_cursor() as (conn, cursor):
                if isinstance(query, str):
                    query = SQL(cast(LiteralString, query))

                self.logger.debug(f"Executing UPDATE query: {query}")
                cursor.execute(query, params)
                conn.commit()
                affected_rows = cursor.rowcount

                execution_time = time.time() - start_time
                return affected_rows

        except IntegrityError as e:
            execution_time = time.time() - start_time
            self.logger.error(f"UPDATE query failed with integrity constraint violation after {execution_time:.3f}s: {e}")
            raise DatabaseError("Integrity constraint violation during UPDATE", e, str(query), params) from e
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"UPDATE query failed after {execution_time:.3f}s: {e}")
            raise

    @retry_on_failure("DELETE query")
    def execute_delete(self, query: Union[str, SQL, Composed], params: tuple[Any, ...] = ()) -> int:
        """
        Execute a DELETE statement and return the number of affected rows.
        Enhanced with comprehensive error handling and logging.
        """
        start_time = time.time()

        try:
            with self._get_cursor() as (conn, cursor):
                if isinstance(query, str):
                    query = SQL(cast(LiteralString, query))

                self.logger.debug(f"Executing DELETE query: {query}")
                cursor.execute(query, params)
                conn.commit()
                affected_rows = cursor.rowcount

                execution_time = time.time() - start_time
                return affected_rows

        except IntegrityError as e:
            execution_time = time.time() - start_time
            self.logger.error(f"DELETE query failed with integrity constraint violation after {execution_time:.3f}s: {e}")
            raise DatabaseError("Integrity constraint violation during DELETE", e, str(query), params) from e
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"DELETE query failed after {execution_time:.3f}s: {e}")
            raise

    @retry_on_failure("INSERT query")
    def execute_insert(self, query: Union[str, SQL, Composed], params: tuple[Any, ...] = ()) -> int:
        """
        Execute an INSERT statement and return the number of affected rows.
        Enhanced with comprehensive error handling and logging.
        """
        start_time = time.time()

        try:
            with self._get_cursor() as (conn, cursor):
                if isinstance(query, str):
                    query = SQL(cast(LiteralString, query))

                self.logger.debug(f"Executing INSERT query: {query}")
                cursor.execute(query, params)
                conn.commit()
                affected_rows = cursor.rowcount

                execution_time = time.time() - start_time
                return affected_rows

        except IntegrityError as e:
            execution_time = time.time() - start_time
            self.logger.error(f"INSERT query failed with integrity constraint violation after {execution_time:.3f}s: {e}")
            raise DatabaseError("Integrity constraint violation during INSERT", e, str(query), params) from e
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"INSERT query failed after {execution_time:.3f}s: {e}")
            raise

    @retry_on_failure("BULK INSERT query")
    def execute_insert_many(self, query: Union[str, SQL, Composed], data_list: list[tuple]) -> int:
        """
        Execute multiple INSERT statements and return the number of affected rows.
        Enhanced with comprehensive error handling, batch processing, and logging.
        """
        if not data_list:
            self.logger.warning("execute_insert_many called with empty data")
            return 0

        start_time = time.time()
        batch_size = 1000  # Process in batches to avoid memory issues
        total_affected = 0

        try:
            with self._get_cursor() as (conn, cursor):
                if isinstance(query, str):
                    query = SQL(cast(LiteralString, query))

                # Process data in batches
                for i in range(0, len(data_list), batch_size):
                    batch = data_list[i:i + batch_size]
                    self.logger.debug(f"Executing BULK INSERT batch {i//batch_size + 1}, size: {len(batch)}")

                    try:
                        cursor.executemany(query, batch)
                        total_affected += cursor.rowcount
                    except Exception as e:
                        self.logger.error(f"Batch {i//batch_size + 1} failed: {e}")
                        # Rollback and re-raise
                        conn.rollback()
                        raise

                conn.commit()
                execution_time = time.time() - start_time
                return total_affected

        except IntegrityError as e:
            execution_time = time.time() - start_time
            self.logger.error(f"BULK INSERT failed with integrity constraint violation after {execution_time:.3f}s: {e}")
            raise DatabaseError("Integrity constraint violation during BULK INSERT", e, str(query), len(data_list)) from e
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"BULK INSERT failed after {execution_time:.3f}s: {e}")
            raise

    def execute_transaction(self, operations: list[tuple[str, Union[str, SQL, Composed], tuple[Any, ...]]]) -> list[Any]:
        """
        Execute multiple operations in a single transaction.

        :param operations: List of tuples (operation_type, query, params)
                          operation_type can be 'select', 'insert', 'update', 'delete'
        :return: List of results from each operation
        """
        if not operations:
            return []

        start_time = time.time()
        results = []

        try:
            with self._get_cursor() as (conn, cursor):
                self.logger.debug(f"Starting transaction with {len(operations)} operations")

                for i, (op_type, query, params) in enumerate(operations):
                    try:
                        if isinstance(query, str):
                            query = SQL(cast(LiteralString, query))

                        cursor.execute(query, params)

                        if op_type.lower() == 'select':
                            results.append(cursor.fetchall())
                        else:
                            results.append(cursor.rowcount)

                    except Exception as e:
                        self.logger.error(f"Transaction operation {i + 1} failed: {e}")
                        conn.rollback()
                        raise DatabaseError(f"Transaction failed at operation {i + 1}", e, str(query), params) from e

                conn.commit()
                execution_time = time.time() - start_time
                return results

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Transaction failed after {execution_time:.3f}s: {e}")
            raise

    def get_metrics(self) -> DatabaseMetrics:
        """Get current database operation metrics"""
        return self.metrics

    def reset_metrics(self):
        """Reset database operation metrics"""
        self.metrics = DatabaseMetrics()

    def close(self):
        """Close the database connection if it's open with proper error handling"""
        try:
            if hasattr(self, 'connection') and self.connection:
                self.connection.close()
        except Exception as e:
            self.logger.warning(f"Error closing database connection: {e}")
        finally:
            self.connection = None
            self.connection_state = ConnectionState.FAILED

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, _exc_tb):
        """Context manager exit with cleanup"""
        self.close()
        if exc_type:
            self.logger.error(f"Exception in context manager: {exc_type.__name__}: {exc_val}")
        return False  # Don't suppress exceptions
