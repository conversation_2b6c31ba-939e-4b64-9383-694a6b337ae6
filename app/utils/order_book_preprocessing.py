import pandas as pd
from typing import Optional


def merge_partial_order_book_snapshots(df: pd.DataFrame, time_window_seconds: int = 10) -> pd.DataFrame:
    """
    Merge partial order book snapshots within a specified time window.

    Args:
        df (pd.DataFrame): DataFrame with columns ['timestamp', 'bids', 'asks'] where bids and asks are lists of dicts.
        time_window_seconds (int): Time window in seconds to merge snapshots.

    Returns:
        pd.DataFrame: DataFrame with merged snapshots, one per time window.
    """
    if df.empty:
        return df

    # Ensure timestamp is datetime and sorted
    df = df.copy()
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp').reset_index(drop=True)

    merged_snapshots = []
    current_window_start = df['timestamp'].iloc[0]
    current_bids = []
    current_asks = []

    def merge_levels(levels1, levels2):
        # Merge two lists of price levels (dicts with 'price' and 'qty')
        # If price exists in both, sum qty, else keep unique
        merged = {}

        # Process first set of levels
        if levels1:
            for level in levels1:
                if isinstance(level, dict) and 'price' in level and 'qty' in level:
                    try:
                        price = float(level['price'])
                        qty = float(level['qty'])
                        if price > 0 and qty > 0:  # Only include valid prices and quantities
                            merged[price] = qty
                    except (ValueError, TypeError):
                        continue  # Skip invalid entries

        # Process second set of levels
        if levels2:
            for level in levels2:
                if isinstance(level, dict) and 'price' in level and 'qty' in level:
                    try:
                        price = float(level['price'])
                        qty = float(level['qty'])
                        if price > 0 and qty > 0:  # Only include valid prices and quantities
                            if price in merged:
                                merged[price] += qty
                            else:
                                merged[price] = qty
                    except (ValueError, TypeError):
                        continue  # Skip invalid entries

        # Convert back to list of dicts
        return [{'price': price, 'qty': qty} for price, qty in merged.items()]

    for idx, row in df.iterrows():
        ts = row['timestamp']

        # Safely get bids and asks, ensuring they are lists
        row_bids = row.get('bids', [])
        row_asks = row.get('asks', [])

        # Ensure bids and asks are lists
        if not isinstance(row_bids, list):
            row_bids = []
        if not isinstance(row_asks, list):
            row_asks = []

        if (ts - current_window_start).total_seconds() <= time_window_seconds:
            # Merge bids and asks
            current_bids = merge_levels(current_bids, row_bids)
            current_asks = merge_levels(current_asks, row_asks)
        else:
            # Append the merged snapshot for the previous window (only if we have data)
            if current_bids or current_asks:
                merged_snapshots.append({
                    'timestamp': current_window_start,
                    'bids': current_bids,
                    'asks': current_asks
                })
            # Reset for new window
            current_window_start = ts
            current_bids = row_bids
            current_asks = row_asks

    # Append the last window (only if we have data)
    if current_bids or current_asks:
        merged_snapshots.append({
            'timestamp': current_window_start,
            'bids': current_bids,
            'asks': current_asks
        })

    if not merged_snapshots:
        # Return empty DataFrame with correct structure if no valid data
        return pd.DataFrame(columns=['timestamp', 'bids', 'asks'])

    merged_df = pd.DataFrame(merged_snapshots)

    # Handle missing bid/ask sides gracefully by filling empty lists
    merged_df['bids'] = merged_df['bids'].apply(lambda x: x if isinstance(x, list) else [])
    merged_df['asks'] = merged_df['asks'].apply(lambda x: x if isinstance(x, list) else [])

    return merged_df
