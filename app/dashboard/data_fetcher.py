import os
import sys
import json
import pandas as pd
import numpy as np
from dotenv import load_dotenv
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# Add the parent directory to sys.path if it's not already there
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from db.db_executor import DatabaseExecutor
from exchange.kraken_api import KrakenAPI

# Load environment variables for database and API credentials
load_dotenv()
DB_NAME = os.getenv('DB_NAME', '')
DB_USER = os.getenv('DB_USER', '')
DB_PASSWORD = os.getenv('DB_PASSWORD', '')
DB_HOST = os.getenv('DB_HOST', '')
DB_PORT = os.getenv('DB_PORT', '')
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY', '')
KRAKEN_API_SECRET = os.getenv('KRAKEN_API_SECRET', '')
API_URL = os.getenv('API_URL', '')

# Load configuration from config.json
with open('config.json', 'r', encoding='utf-8') as config_file:
    config = json.load(config_file)
mode = config['mode']
pair = config['pair']
currency = config['currency']
initial_cash = config.get('initial_portfolio', 5000)
use_simulator = config.get('use_simulator', False)
simulator_data_file = config.get('simulator_data_file', None)

db = DatabaseExecutor(DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT)
kraken = KrakenAPI(KRAKEN_API_KEY, KRAKEN_API_SECRET, API_URL, use_simulator, simulator_data_file)

# --- Data Fetching Functions ---

def fetch_latest_price(pair, mode):
    """Fetch the latest price for the trading pair."""
    if mode == 'live':
        ticker = kraken.get_ticker(pair)  # Use get_ticker instead of get_ticker_information
        return float(ticker['result'][pair]['c'][0])  # Last trade price
    elif mode == 'paper':
        query = "SELECT close_price FROM kraken_ohlc WHERE pair = %s ORDER BY timestamp DESC LIMIT 1"
        result = db.execute_select(query, (pair,))
        return float(result[0][0]) if result else None

def fetch_account_balance(mode, currency, initial_cash=10000):

    if mode == 'live':
        # Fetch balance directly from Kraken API for live trading
        balance = kraken.get_account_balance()
        return float(balance['result'].get('Z' + currency, 0))
    elif mode == 'paper':
        # Calculate balance from paper_trades table
        query = """
        SELECT SUM(CASE WHEN buy_sell = 'buy' THEN -price * volume - fee
                       ELSE price * volume - fee END) AS cash_change
        FROM paper_trades
        """
        result = db.execute_select(query, float_columns=list(range(1)))
        cash_change = result[0][0] if result and result[0][0] is not None else 0
        return initial_cash + cash_change


def fetch_open_positions(mode):

#    if mode == 'live':
#        # Fetch open positions from Kraken API for live trading
#        positions = kraken.get_open_positions()
#        return positions['result'] if positions.get('result') else {}
#    elif mode == 'paper':
        # Fetch open positions from paper_trades where trades are not closed
    table = 'live_trades' if mode == 'live' else 'paper_trades'
    table_results = 'live_results' if mode == 'live' else 'paper_results'
    query = f"""
            SELECT
                t.trade_id,
                t.trade_id_match,
                t.price AS entry_price,
                (t.volume - COALESCE(r.sold_volume, 0)) AS volume
            FROM {table} t
            LEFT JOIN (
                SELECT entry_trade_id, strategy_name, SUM(volume) AS sold_volume, SUM(entry_fee) AS sold_fee
                FROM {table_results}
                GROUP BY entry_trade_id, strategy_name
            ) r ON t.trade_id_match = r.entry_trade_id
            WHERE t.buy_sell = 'buy'
            AND (t.volume - COALESCE(r.sold_volume, 0)) > 0
        """
    df = pd.DataFrame(db.execute_select(query), columns=['trade_id', 'trade_id_match', 'entry_price', 'volume'])
    return df.to_dict('records')

def fetch_closed_trades(mode):
    """Fetch closed trades from the appropriate results table."""
    table = 'live_results' if mode == 'live' else 'paper_results'
    query = f"""
    SELECT entry_timestamp, exit_timestamp, profit_loss, duration, success
    FROM {table}
    WHERE exit_timestamp IS NOT NULL
    """
    df = pd.DataFrame(
        db.execute_select(query), columns=['entry_timestamp', 'exit_timestamp', 'profit_loss', 'duration', 'success']
    )
    return df

def compute_historical_portfolio_value(pair, mode, initial_cash):
    """Compute historical portfolio value using trades and OHLC data."""
    table = 'live_trades' if mode == 'live' else 'paper_trades'
    trades_query = f"""
    SELECT timestamp, price, volume, buy_sell, fee
    FROM {table}
    WHERE pair = %s
    ORDER BY timestamp
    """
    trades = pd.DataFrame(
        db.execute_select(trades_query, (pair,))
    )

    ohlc_query = """
    SELECT timestamp, close_price
    FROM kraken_ohlc
    WHERE pair = %s
    ORDER BY timestamp
    """
    ohlc = pd.DataFrame(
        db.execute_select(ohlc_query, (pair,))
    )

    # Merge trades with OHLC for continuous price data
    if trades.empty or ohlc.empty:
        return pd.DataFrame(columns=['timestamp', 'portfolio_value'])

    ohlc.columns = ['timestamp', 'close_price']
    trades.columns = ['timestamp', 'price', 'volume', 'buy_sell', 'fee']
    trades.sort_values('timestamp', inplace=True)
    ohlc.sort_values('timestamp', inplace=True)

    df = pd.merge_asof(trades, ohlc, on='timestamp', direction='backward')

    # Ensure numeric types for calculations
    df['price'] = pd.to_numeric(df['price'])
    df['volume'] = pd.to_numeric(df['volume'])
    df['close_price'] = pd.to_numeric(df['close_price'])
    df['fee'] = pd.to_numeric(df['fee'])

    # Initialize portfolio tracking columns
    df['cash'] = float(initial_cash)
    df['position'] = 0.0
    df['portfolio_value'] = 0.0

    # Convert index to integer to avoid type issues with loc
    df = df.reset_index(drop=True)

    # Process each trade sequentially
    cash = float(initial_cash)
    position = 0.0

    for idx in range(len(df)):
        # Get row data
        row = df.iloc[idx]

        # Decode buy_sell if needed
        buy_sell = row['buy_sell'].decode('utf-8') if isinstance(row['buy_sell'], bytes) else row['buy_sell']

        # Get values as Python primitives
        price = float(row['price'])
        volume = float(row['volume'])
        close_price = float(row['close_price'])
        fee = float(row['fee'])

        # Update cash and position based on trade
        if buy_sell == 'buy':
            cash -= (price * volume + fee)
            position += volume
        elif buy_sell == 'sell':
            cash += (price * volume - fee)
            position -= volume

        # Store updated values in DataFrame
        df.at[idx, 'cash'] = cash
        df.at[idx, 'position'] = position
        df.at[idx, 'portfolio_value'] = cash + (position * close_price)

    return df[['timestamp', 'portfolio_value']]

def fetch_metrics(pair, mode, currency, initial_portfolio):
    """Fetch and compute real-time trading metrics."""
    current_price = fetch_latest_price(pair, mode)
    cash = fetch_account_balance(mode, currency, initial_portfolio)
    open_positions = fetch_open_positions(mode)

    # Compute positions value and unrealized PnL
    positions_value = 0.0
    unrealized_pnl = 0.0
    positions_volume = 0.0

    # Handle case where current_price might be None
    if current_price is not None:
        for pos_tx in open_positions:
            pos = pos_tx
            if mode == 'live-futures':
                volume = float(pos['vol'])
                cost = float(pos['cost'])
                entry_price = cost / volume
            else:
                volume = float(pos['volume'])
                entry_price = float(pos['entry_price'])
            positions_volume += volume
            positions_value += volume * current_price
            unrealized_pnl += (current_price - entry_price) * volume

    # Calculate portfolio value
    portfolio_value = cash + positions_value if cash is not None else positions_value

    # Process closed trades
    closed_trades = fetch_closed_trades(mode)
    realized_pnl = closed_trades['profit_loss'].sum() if not closed_trades.empty else 0
    num_trades = len(closed_trades)
    win_rate = (closed_trades['success'].sum() / num_trades * 100) if num_trades > 0 else 0

    # Handle duration calculation - simplify to avoid type issues
    avg_trade_duration = 0
    if not closed_trades.empty:
        # Just convert to float and assume it's in seconds
        try:
            # Get the mean duration as a float value
            avg_trade_duration = float(closed_trades['duration'].mean()) / 3600  # Convert to hours
        except (ValueError, TypeError):
            # If conversion fails, default to 0
            avg_trade_duration = 0

    portfolio_history = compute_historical_portfolio_value(pair, mode, initial_cash)
    if not portfolio_history.empty:
        peak = portfolio_history['portfolio_value'].cummax()
        drawdown = (peak - portfolio_history['portfolio_value']) / peak * 100
        max_drawdown = drawdown.max()
        total_return = (
            (portfolio_value - portfolio_history['portfolio_value'].iloc[0]) /
            portfolio_history['portfolio_value'].iloc[0] * 100
        )
        returns = portfolio_history['portfolio_value'].pct_change().dropna()
        sharpe_ratio = (returns.mean() / returns.std()) * np.sqrt(252) if len(returns) > 1 else 0
    else:
        drawdown = pd.Series([0])
        max_drawdown = 0
        total_return = 0
        sharpe_ratio = 0

    metrics = {
        'portfolio_value': portfolio_value,
        'cash': cash,
        'asset_balance': positions_volume,
        'current_price': current_price,
        'positions_value': positions_value,
        'drawdown': drawdown.iloc[-1],
        'max_drawdown': max_drawdown,
        'total_return': total_return,
        'realized_pnl': realized_pnl,
        'unrealized_pnl': unrealized_pnl,
        'win_rate': win_rate,
        'sharpe_ratio': sharpe_ratio,
        'num_open_positions': len(open_positions),
        'num_trades': num_trades,
        'avg_trade_duration': avg_trade_duration
    }
    return metrics