import logging
from typing import Optional
from datetime import datetime
from app.exchange.kraken_api import KrakenAPI
from app.scripts.base_order_executor import OrderExecutor


class RealOrderExecutor(OrderExecutor):
    def __init__(self, api: KrakenAPI):
        """
        Initialize the RealOrderExecutor with Kraken API credentials.

        Args:
            api_key (str): Kraken API key.
            api_secret (str): Kraken API secret.
            api_url (str): Kraken API URL.
        """
        self.api = api
        # Track pending orders by txid
        self.pending_orders: dict[str, dict] = {}
        self.logger = logging.getLogger(__name__)  # Initialize logger

    def place_order(self, order_type: str, price: float, volume: float, timestamp: datetime, pair: Optional[str] = None) -> dict:
        """
        Place a real order via Kraken API with enhanced error checking.

        Args:
            order_type (str): 'buy' or 'sell'.
            price (float): Price at which to place the order.
            volume (float): Volume to trade.
            timestamp (datetime): Timestamp of the order.
            pair (str, optional): Trading pair symbol. If None, uses default from API.

        Returns:
            dict: Order placement details, including txid.

        Raises:
            Exception: If the order fails or the API response is invalid.
        """
        # Get the pair from the parameter, API configuration, or use a default
        if pair is None:
            pair = self.api.default_pair if hasattr(
                self.api, 'default_pair') else 'XBTUSD'

        # Place the order using the Kraken API
        if round(price, 2) != price:
            price = round(price, 2)
        order_response = self.api.add_order(
            pair=pair,
            order_type=order_type,
            ordertype='limit',
            price=str(price),
            volume=str(volume),
            expiretm='+3300'
        )

        # Error checking: Check for API errors
        if 'error' in order_response and order_response['error']:
            self.logger.error(
                f"Failed to place order: {order_response['error']}")
            raise Exception(
                f"Failed to place order: {order_response['error']}")

        # Validate response structure
        if 'result' not in order_response or 'txid' not in order_response['result'] or not order_response['result']['txid']:
            self.logger.error(
                "Invalid response from Kraken API: missing 'result' or 'txid'")
            raise Exception(
                "Invalid response from Kraken API: missing 'result' or 'txid'")

        # Extract transaction ID and store order details
        txid = order_response['result']['txid'][0]
        self.pending_orders[txid] = {
            'pair': pair,
            'order_type': order_type,
            'price': price,
            'volume': volume,
            'timestamp': timestamp
        }

        # Log the order placement
        self.logger.info(
            f"Placed {order_type} order for {pair} at price {price} with volume {volume}, txid: {txid}")
        return {'status': 'pending', 'txid': txid}

    def check_pending_orders(self, current_time: datetime) -> list:
        """
        Check the status of pending orders and return filled or expired ones with enhanced error checking.

        Args:
            current_time (datetime): Current timestamp (unused in this implementation).

        Returns:
            list: List of processed orders with details.
        """
        processed_orders = []
        to_remove = []
        try:
            # Iterate over pending orders
            for txid in list(self.pending_orders.keys()):
                order_info = self.api.query_orders_info(txid)

                # Error checking: Handle API errors
                if 'error' in order_info and order_info['error']:
                    self.logger.error(
                        f"Error querying order {txid}: {order_info['error']}")
                    continue

                # Validate response structure
                if 'result' not in order_info or txid not in order_info['result']:
                    self.logger.error(
                        f"Invalid response for order {txid}: missing 'result' or order data")
                    continue

                # Process the order status
                order = order_info['result'][txid]
                order_status = order['status']
                vol = float(order.get('vol', 0))
                vol_exec = float(order.get('vol_exec', 0))
                cost = float(order.get('cost', 0))
                fee = float(order.get('fee', 0))
                price = float(order.get('price', 0))

                if order_status == 'closed':  # Order is fully filled
                    processed_order = {
                        'txid': txid,
                        'status': 'filled',
                        'pair': self.pending_orders[txid]['pair'],
                        'order_type': self.pending_orders[txid]['order_type'],
                        'price': price,
                        'volume': vol,
                        'vol_exec': vol_exec,
                        'cost': cost,
                        'fee': fee,
                        'timestamp': datetime.fromtimestamp(order['closetm']) if 'closetm' in order else current_time
                    }
                    processed_orders.append(processed_order)
                    to_remove.append(txid)
                    self.logger.info(f"Order {txid} filled: {processed_order}")

                elif order_status in ['canceled', 'expired']:
                    # Check for partial fill
                    if vol_exec > 0:
                        status = 'partially_filled_then_' + order_status
                    else:
                        status = order_status
                    processed_order = {
                        'txid': txid,
                        'status': status,
                        'pair': self.pending_orders[txid]['pair'],
                        'order_type': self.pending_orders[txid]['order_type'],
                        'price': price,
                        'volume': vol,
                        'vol_exec': vol_exec,
                        'cost': cost,
                        'fee': fee,
                        'timestamp': current_time
                    }
                    processed_orders.append(processed_order)
                    to_remove.append(txid)
                    self.logger.info(
                        f"Order {txid} {status}: {processed_order}")

        except Exception as e:
            self.logger.error(
                f"Error checking pending orders: {e}", exc_info=True)

        # Remove processed orders from pending list
        for txid in to_remove:
            del self.pending_orders[txid]

        return processed_orders
