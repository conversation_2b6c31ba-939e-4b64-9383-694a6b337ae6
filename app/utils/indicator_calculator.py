import pandas as pd
import numpy as np

# pyright: reportAttributeAccessIssue=false

import talib
from talib._ta_lib import MA_Type


class IndicatorCalculator:

    @staticmethod
    def calculate_sma(df: pd.DataFrame, period: int, column: str = "close_price") -> pd.Series:
        """Calculate Simple Moving Average (SMA)."""
        values = df[column].astype(float).to_numpy(dtype=np.float64)
        sma = talib.SMA(values, timeperiod=period)
        return pd.Series(sma, index=df.index)

    @staticmethod
    def calculate_ema(df: pd.DataFrame, period: int, column: str = "close_price") -> pd.Series:
        values = df[column].astype(float).to_numpy(dtype=np.float64)
        ema = talib.EMA(values, timeperiod=period)
        return pd.Series(ema, index=df.index)

    @staticmethod
    def calculate_rsi(df: pd.DataFrame, period: int = 14, column: str = "close_price") -> pd.Series:
        values = df[column].astype(float).to_numpy(dtype=np.float64)
        rsi = talib.RSI(values, timeperiod=period)
        return pd.Series(rsi, index=df.index)

    @staticmethod
    def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
        high = df['high_price'].astype(float).to_numpy(dtype=np.float64)
        low = df['low_price'].astype(float).to_numpy(dtype=np.float64)
        close = df['close_price'].astype(float).to_numpy(dtype=np.float64)
        atr = talib.ATR(high, low, close, timeperiod=period)
        return pd.Series(atr, index=df.index)

    @staticmethod
    def calculate_vwap(df: pd.DataFrame, price_column: str = 'close_price', volume_column: str = 'volume') -> pd.Series:
        df['Price_Volume'] = df[price_column] * df[volume_column]
        df['Cumulative_Price_Volume'] = df['Price_Volume'].cumsum()
        df['Cumulative_Volume'] = df[volume_column].cumsum()
        df['VWAP'] = np.where(df['Cumulative_Volume'] != 0,
                              df['Cumulative_Price_Volume'] /
                              df['Cumulative_Volume'],
                              np.nan)
        return df['VWAP']

    @staticmethod
    def calculate_macd(df: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9,
                       price_column: str = 'close_price') -> tuple:
        """Calculate MACD (Moving Average Convergence Divergence)."""
        values = df[price_column].astype(np.float64).to_numpy()
        macd, signal, hist = talib.MACD(values,
                                        fastperiod=fast_period,
                                        slowperiod=slow_period,
                                        signalperiod=signal_period)
        return pd.Series(macd, index=df.index), pd.Series(signal, index=df.index), pd.Series(hist, index=df.index)

    @staticmethod
    def calculate_stochastic(df: pd.DataFrame, k_period: int = 14, d_period: int = 3,
                             smooth_k: int = 3) -> tuple:
        """Calculate Stochastic Oscillator."""
        high = df['high_price'].astype(np.float64).to_numpy()
        low = df['low_price'].astype(np.float64).to_numpy()
        close = df['close_price'].astype(np.float64).to_numpy()
        stoch_k, stoch_d = talib.STOCH(high,
                                       low,
                                       close,
                                       fastk_period=k_period,
                                       slowk_period=smooth_k,
                                       slowk_matype=MA_Type.SMA,
                                       slowd_period=d_period,
                                       slowd_matype=MA_Type.SMA)
        return pd.Series(stoch_k, index=df.index), pd.Series(stoch_d, index=df.index)

    @staticmethod
    def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, num_std: float = 2.0,
                                  price_column: str = 'close_price') -> tuple:
        """Calculate Bollinger Bands."""
        values = df[price_column].astype(float).to_numpy(dtype=np.float64)
        upper, middle, lower = talib.BBANDS(values,
                                            timeperiod=period,
                                            nbdevup=num_std,
                                            nbdevdn=num_std,
                                            matype=MA_Type.SMA)
        return pd.Series(upper, index=df.index), pd.Series(middle, index=df.index), pd.Series(lower, index=df.index)

    @staticmethod
    def calculate_money_flow_index(df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Money Flow Index."""
        high = df['high_price'].astype(np.float64).to_numpy()
        low = df['low_price'].astype(np.float64).to_numpy()
        close = df['close_price'].astype(np.float64).to_numpy()
        volume = df['volume'].astype(np.float64).to_numpy()
        mfi = talib.MFI(high, low, close, volume, timeperiod=period)
        return pd.Series(mfi, index=df.index)

    @staticmethod
    def calculate_average_directional_index(df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average Directional Index (ADX)."""
        high = df['high_price'].astype(np.float64).to_numpy()
        low = df['low_price'].astype(np.float64).to_numpy()
        close = df['close_price'].astype(np.float64).to_numpy()
        adx = talib.ADX(high, low, close, timeperiod=period)
        return pd.Series(adx, index=df.index)

    @staticmethod
    def calculate_on_balance_volume(df: pd.DataFrame, price_column: str = 'close_price',
                                    volume_column: str = 'volume') -> pd.Series:
        """Calculate On Balance Volume (OBV)."""
        price = df[price_column].astype(np.float64).to_numpy()
        volume = df[volume_column].astype(np.float64).to_numpy()
        obv = talib.OBV(price, volume)
        return pd.Series(obv, index=df.index)

    @staticmethod
    def calculate_relative_volume(df: pd.DataFrame, period: int = 20,
                                  volume_column: str = 'volume') -> pd.Series:
        """Calculate Relative Volume compared to moving average."""
        volume_ma = df[volume_column].rolling(window=period).mean()
        relative_volume = df[volume_column] / volume_ma
        return relative_volume

    @staticmethod
    def calculate_momentum(df: pd.DataFrame, period: int = 10,
                           price_column: str = 'close_price') -> pd.Series:
        """Calculate Momentum indicator."""
        return df[price_column].diff(period)

    @staticmethod
    def calculate_price_channels(df: pd.DataFrame, period: int = 20) -> tuple:
        """Calculate Price Channels."""
        upper = df['high_price'].rolling(window=period).max()
        lower = df['low_price'].rolling(window=period).min()
        middle = (upper + lower) / 2
        return upper, middle, lower

    @staticmethod
    def detect_market_regime(df: pd.DataFrame, volatility_window: int = 20,
                             trend_threshold: float = 0.02) -> pd.Series:
        """
        Detect market regime (trending, ranging, or volatile).
        Returns a series with values:
        1 = trending up
        0 = ranging
        -1 = trending down
        2 = volatile
        """
        # Calculate volatility
        volatility = df['close_price'].pct_change().rolling(
            window=volatility_window).std()
        volatility_threshold = volatility.mean() * 1.5

        # Calculate trend
        ema20 = talib.EMA(df['close_price'].astype(float).to_numpy(dtype=np.float64), timeperiod=20)
        ema50 = talib.EMA(df['close_price'].astype(float).to_numpy(dtype=np.float64), timeperiod=50)

        regime = pd.Series(index=df.index, data=0.0)  # Default to ranging

        # Identify regimes
        regime[volatility > volatility_threshold] = 2  # Volatile
        regime[(ema20 > ema50 * (1 + trend_threshold)) &
               (volatility <= volatility_threshold)] = 1  # Trending up
        regime[(ema20 < ema50 * (1 - trend_threshold)) &
               (volatility <= volatility_threshold)] = -1  # Trending down

        return regime

    @staticmethod
    def detect_candlestick_patterns(df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect major candlestick patterns using TA-Lib and add them as columns.
        Patterns included:
        - Reversals: Bullish/Bearish Engulfing, Hammer, Hanging Man, Doji, Morning Star, Evening Star
        - Continuations: Marubozu, Three White Soldiers, Three Black Crows
        """
        df = df.copy()

        # Convert columns to numpy float64 arrays for TA-Lib
        open_price = df['open_price'].astype(float).to_numpy(dtype=np.float64)
        high_price = df['high_price'].astype(float).to_numpy(dtype=np.float64)
        low_price = df['low_price'].astype(float).to_numpy(dtype=np.float64)
        close_price = df['close_price'].astype(float).to_numpy(dtype=np.float64)

        # Reversal patterns
        df['bullish_engulfing'] = talib.CDLENGULFING(
            open_price, high_price, low_price, close_price) > 0
        df['bearish_engulfing'] = talib.CDLENGULFING(
            open_price, high_price, low_price, close_price) < 0
        df['hammer'] = talib.CDLHAMMER(
            open_price, high_price, low_price, close_price) != 0
        df['hanging_man'] = talib.CDLHANGINGMAN(
            open_price, high_price, low_price, close_price) != 0
        df['doji'] = talib.CDLDOJI(
            open_price, high_price, low_price, close_price) != 0
        df['morning_star'] = talib.CDLMORNINGSTAR(
            open_price, high_price, low_price, close_price) != 0
        df['evening_star'] = talib.CDLEVENINGSTAR(
            open_price, high_price, low_price, close_price) != 0

        # Continuation patterns
        df['marubozu'] = talib.CDLMARUBOZU(
            open_price, high_price, low_price, close_price) != 0
        df['three_white_soldiers'] = talib.CDL3WHITESOLDIERS(
            open_price, high_price, low_price, close_price) != 0
        df['three_black_crows'] = talib.CDL3BLACKCROWS(
            open_price, high_price, low_price, close_price) != 0

        return df

    @staticmethod
    def calculate_volume_roc(df: pd.DataFrame, period: int = 14,
                             volume_column: str = 'volume') -> pd.Series:
        """Calculate Volume Rate of Change (ROC)."""
        values = df[volume_column].astype(float).to_numpy(dtype=np.float64)
        roc = talib.ROC(values, timeperiod=period)
        return pd.Series(roc, index=df.index)

    @staticmethod
    def calculate_stddev_of_returns(df: pd.DataFrame, period: int = 14,
                                    price_column: str = 'close_price') -> pd.Series:
        """Calculate Standard Deviation of Returns."""
        df[price_column] = df[price_column].astype(float)
        # use pct_change to maintain same length
        returns = df[price_column].pct_change().astype(float).to_numpy(dtype=np.float64)
        stddev = talib.STDDEV(returns, timeperiod=period)

        return pd.Series(stddev, index=df.index)

    @staticmethod
    def calculate_keltner_channels(df: pd.DataFrame,
                                   ema_period: int = 20,
                                   atr_period: int = 10,
                                   price_column: str = 'close_price') -> tuple:
        """
        Calculate Keltner Channels: Middle (EMA), Upper, and Lower bands.
        upper = EMA + 2 * ATR
        lower = EMA - 2 * ATR
        """
        middle_line = talib.EMA(df[price_column].astype(float).to_numpy(dtype=np.float64), timeperiod=ema_period)
        atr = talib.ATR(df['high_price'].astype(float).to_numpy(dtype=np.float64), df['low_price'].astype(float).to_numpy(dtype=np.float64),
                        df[price_column].astype(float).to_numpy(dtype=np.float64), timeperiod=atr_period)
        upper_line = middle_line + 2 * atr
        lower_line = middle_line - 2 * atr
        return upper_line, middle_line, lower_line

    @staticmethod
    def calculate_parabolic_sar(df: pd.DataFrame,
                                acceleration: float = 0.02,
                                maximum: float = 0.2) -> pd.Series:
        """
        Calculate Parabolic SAR (Stop and Reverse).

        Args:
            df: DataFrame containing high and low prices
            acceleration: Acceleration factor (default 0.02)
            maximum: Maximum acceleration factor (default 0.2)

        Returns:
            Series with Parabolic SAR values
        """
        sar_values = talib.SAR(df['high_price'].astype(float).to_numpy(dtype=np.float64), df['low_price'].astype(float).to_numpy(dtype=np.float64),
                         acceleration=acceleration,
                         maximum=maximum)
        return pd.Series(sar_values, index=df.index)

    @staticmethod
    def calculate_commodity_channel_index(df: pd.DataFrame,
                                          period: int = 20) -> pd.Series:
        """
        Calculate Commodity Channel Index (CCI).

        Args:
            df: DataFrame containing high, low, and close prices
            period: Lookback period (default 20)

        Returns:
            Series with CCI values
        """
        high = df['high_price'].astype(float).to_numpy(dtype=np.float64)
        low = df['low_price'].astype(float).to_numpy(dtype=np.float64)
        close = df['close_price'].astype(float).to_numpy(dtype=np.float64)
        cci = talib.CCI(high, low, close, timeperiod=period)
        return pd.Series(cci, index=df.index)

    @staticmethod
    def calculate_rate_of_change(df: pd.DataFrame,
                                 period: int = 10,
                                 price_column: str = 'close_price') -> pd.Series:
        """
        Calculate Rate of Change (ROC) indicator.

        Args:
            df: DataFrame containing price data
            period: Lookback period (default 10)
            price_column: Column name for price data (default 'close_price')

        Returns:
            Series with ROC values
        """
        values = df[price_column].astype(float).to_numpy(dtype=np.float64)
        roc = talib.ROC(values, timeperiod=period)
        return pd.Series(roc, index=df.index)

    @staticmethod
    def calculate_chaikin_ad_line(df: pd.DataFrame) -> pd.Series:
        """
        Calculate Chaikin Accumulation/Distribution Line (AD).

        Args:
            df: DataFrame containing high, low, close prices and volume

        Returns:
            Series with AD Line values
        """
        high = df['high_price'].astype(float).to_numpy(dtype=np.float64)
        low = df['low_price'].astype(float).to_numpy(dtype=np.float64)
        close = df['close_price'].astype(float).to_numpy(dtype=np.float64)
        volume = df['volume'].astype(float).to_numpy(dtype=np.float64)
        ad = talib.AD(high, low, close, volume)
        return pd.Series(ad, index=df.index)

    @staticmethod
    def calculate_chaikin_oscillator(df: pd.DataFrame,
                                     fast_period: int = 3,
                                     slow_period: int = 10) -> pd.Series:
        """
        Calculate Chaikin Oscillator (ADOSC).

        Args:
            df: DataFrame containing high, low, close prices and volume
            fast_period: Fast EMA period (default 3)
            slow_period: Slow EMA period (default 10)

        Returns:
            Series with Chaikin Oscillator values
        """
        high = df['high_price'].astype(float).to_numpy(dtype=np.float64)
        low = df['low_price'].astype(float).to_numpy(dtype=np.float64)
        close = df['close_price'].astype(float).to_numpy(dtype=np.float64)
        volume = df['volume'].astype(float).to_numpy(dtype=np.float64)
        adosc = talib.ADOSC(high, low, close, volume,
                           fastperiod=fast_period,
                           slowperiod=slow_period)
        return pd.Series(adosc, index=df.index)

    @staticmethod
    def calculate_bollinger_percent_b(df: pd.DataFrame,
                                      period: int = 20,
                                      num_std: float = 2.0,
                                      price_column: str = 'close_price') -> pd.Series:
        """
        Calculate Bollinger %B (position within Bollinger Bands).

        Args:
            df: DataFrame containing price data
            period: Lookback period for Bollinger Bands (default 20)
            num_std: Number of standard deviations for bands (default 2.0)
            price_column: Column name for price data (default 'close_price')

        Returns:
            Series with %B values (0-1 when within bands, can exceed beyond)
        """
        values = df[price_column].astype(float).to_numpy(dtype=np.float64)
        upper, _, lower = talib.BBANDS(values,
                                       timeperiod=period,
                                       nbdevup=num_std,
                                       nbdevdn=num_std,
                                       matype=MA_Type.SMA)
        return (df[price_column] - pd.Series(lower, index=df.index)) / (pd.Series(upper, index=df.index) - pd.Series(lower, index=df.index))

    @staticmethod
    def calculate_volatility_ratio(df: pd.DataFrame,
                                   short_period: int = 5,
                                   long_period: int = 20,
                                   price_column: str = 'close_price') -> pd.Series:
        """
        Calculate Volatility Ratio (short-term vs long-term volatility).

        Args:
            df: DataFrame containing price data
            short_period: Short-term lookback (default 5)
            long_period: Long-term lookback (default 20)
            price_column: Column name for price data (default 'close_price')

        Returns:
            Series with volatility ratio values
        """
        short_std = df[price_column].rolling(window=short_period).std()
        long_std = df[price_column].rolling(window=long_period).std()
        volatility_ratio = short_std / long_std
        return volatility_ratio.fillna(1.0)

    @staticmethod
    def calculate_mid_price(df: pd.DataFrame) -> pd.Series:
        """Calculate the Mid Price (average of best bid and ask)."""
        return (df['best_bid_price'] + df['best_ask_price']) / 2

    @staticmethod
    def calculate_mid_price_movement(df: pd.DataFrame, period: int = 5) -> pd.Series:
        """Calculate Mid-Price movement (momentum of mid-price)."""
        mid_price = IndicatorCalculator.calculate_mid_price(df)
        return mid_price.diff(period)

    @staticmethod
    def calculate_order_flow_imbalance(df: pd.DataFrame, depth_levels: int = 5) -> pd.Series:
        """
        Calculate order flow imbalance (OFI) over top N levels.
        Assumes df has columns like: bid_vol_1, bid_vol_2, ..., ask_vol_1, ask_vol_2, ...
        """
        bid_cols = [f'bid_vol_{i}' for i in range(1, depth_levels + 1)]
        ask_cols = [f'ask_vol_{i}' for i in range(1, depth_levels + 1)]

        bid_sum = df[bid_cols].sum(axis=1)
        ask_sum = df[ask_cols].sum(axis=1)

        imbalance = (bid_sum - ask_sum) / (bid_sum +
                                           ask_sum + 1e-9)  # safe division
        return imbalance

    @staticmethod
    def calculate_liquidity_depletion(df: pd.DataFrame, depth_levels: int = 5, window: int = 5) -> pd.Series:
        """
        Measure liquidity depletion (how fast top N liquidity disappears).
        Calculates % change of sum(top N bid + ask volumes) over a rolling window.
        """
        bid_cols = [f'bid_vol_{i}' for i in range(1, depth_levels + 1)]
        ask_cols = [f'ask_vol_{i}' for i in range(1, depth_levels + 1)]

        total_liquidity = df[bid_cols + ask_cols].sum(axis=1)
        depletion_rate = total_liquidity.pct_change(periods=window)
        return depletion_rate

    @staticmethod
    def calculate_vwap_slope(df: pd.DataFrame, window: int = 5) -> pd.Series:
        """Calculate the slope of VWAP over a rolling window."""
        vwap = IndicatorCalculator.calculate_vwap(df)
        slope = vwap.diff(window) / window
        return slope

    @staticmethod
    def calculate_obv_slope(df: pd.DataFrame, window: int = 5) -> pd.Series:
        """Calculate the slope of OBV (On-Balance Volume) over a rolling window."""
        obv = IndicatorCalculator.calculate_on_balance_volume(df)
        slope = obv.diff(window) / window
        return slope

    @staticmethod
    def calculate_relative_strength(df: pd.DataFrame, asset_rsi: pd.Series, market_rsi: pd.Series) -> pd.Series:
        """
        Calculate relative strength between asset and the market.
        asset_rsi: RSI of the asset
        market_rsi: RSI of the benchmark (or mean RSI of several assets)

        Note: df parameter is kept for API consistency but not used
        """
        # Suppress unused parameter warning
        _ = df
        return asset_rsi - market_rsi
