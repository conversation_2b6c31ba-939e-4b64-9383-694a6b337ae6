FROM python:3.13-slim-bookworm

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    USER=app

ENV HOME=/home/<USER>
    PATH=/home/<USER>/.local/bin:/usr/local/bin:$PATH

# Create a non-root user and set permissions
RUN adduser --uid 2000 --gecos "" --disabled-password $USER \
    && mkdir -p $HOME/project \
    && mkdir -p $HOME/.local \
    && chown -R $USER:$USER $HOME

# Switch to non-root user
USER $USER

# Upgrade pip
RUN python -m pip install --user --upgrade pip

# Set the working directory
WORKDIR $HOME

# Set Python path to include the project directory
ENV PYTHONPATH="/home/<USER>/project"

# Default command - will be overridden by docker-compose
C<PERSON> ["python", "--version"]