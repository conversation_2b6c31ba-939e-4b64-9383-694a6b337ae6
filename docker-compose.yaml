
services:
  db:
    image: postgres:16
    container_name: postgres_db
    environment:
      POSTGRES_DB: crypto
      POSTGRES_USER: crypto
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5452:5432"
    networks:
      - crypto_net

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pytrader_app
    environment:
      - DB_HOST=db
      - DB_NAME=crypto
      - DB_USER=crypto
      - DB_PASSWORD=${DB_PASSWORD}
    volumes:
      - /home/<USER>/pyTrader:/home/<USER>/scripts/:rw
      - /home/<USER>/pyTrader/config.json:/home/<USER>/scripts/config.json:ro
      - /home/<USER>/pyTrader/.env:/home/<USER>/scripts/.env:ro
      - /home/<USER>/pyTrader/logs:/home/<USER>/scripts/logs
    # No ports exposed as per user request
    #depends_on:
    #  - db
    networks:
      - crypto_net

  dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    container_name: pytrader_dashboard
    environment:
      - DB_HOST=db
      - DB_NAME=crypto
      - DB_USER=crypto
      - DB_PASSWORD=${DB_PASSWORD}
    volumes:
      # Mount the entire project directory to a different location
      - /home/<USER>/pyTrader:/home/<USER>/project:rw
      # Create a volume for installed packages
      - dashboard_packages:/home/<USER>/.local:rw
    ports:
      - "127.0.0.1:8050:8050"
    command: >
      sh -c "
      pip install --user -r /home/<USER>/project/app/requirements_dashboard.txt &&
      cd /home/<USER>/project &&
      python app/dashboard/dashboard.py > logs/dashboard.log 2>&1
      "
    #depends_on:
    #  - db
    networks:
      - crypto_net

volumes:
  postgres_data:  # Data volume for PostgreSQL persistence
  dashboard_packages:  # Volume for installed packages

networks:
  crypto_net:  # Create a network for the app and db containers to communicate
