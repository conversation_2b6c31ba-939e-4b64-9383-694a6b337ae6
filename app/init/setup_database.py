import os
import logging
from os import getenv
from ..db_setup.db_setup import DatabaseSetup

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


if __name__ == "__main__" or os.getenv("RUN_MAIN"):
    env = getenv("ENVIRONMENT")

    # PostgreSQL connection details
    DB_NAME = getenv('DB_NAME')
    DB_USER = getenv('DB_USER')
    DB_PASSWORD = getenv('DB_PASSWORD')
    DB_HOST = getenv('DB_HOST')
    DB_PORT = getenv('DB_PORT')

    ds = DatabaseSetup(DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT)
    ds.create_tables()
    ds.update_existing_tables()
    ds.close()
    logger.info('Database created !')
