import json
import time
import uuid
from typing import Any, Optional
from decimal import Decimal


class KrakenSimulator:
    def __init__(self, initial_data_file="kraken_simulator_data.json", fill_delay: float = 5.0):
        """
        Initialize the KrakenSimulator.

        Args:
            initial_data_file (str): File to load initial data from.
            fill_delay (float): Time in seconds after which open orders are filled.
        """
        # Load initial account data from file or use defaults
        if initial_data_file:
            try:
                with open(initial_data_file, 'r', encoding='utf-8') as f:
                    self.simulator_data = json.load(f)
            except FileNotFoundError:
                self.simulator_data = self._default_data()
        else:
            self.simulator_data = self._default_data()

        # Define some default asset prices for simulation
        self.prices = {
            "XXBTZUSD": "50000.0",  # BTC/USD price
            "XETHZUSD": "3000.0",   # ETH/USD price
            "XXBTXETH": "16.67",    # BTC/ETH price
            "XSOLZUSD": "131.67"    # SOL/USD price
        }

        self.fill_delay = fill_delay  # Time in seconds to fill orders

    def _default_data(self) -> dict[str, Any]:
        """Return default data structure if no file is provided or found."""
        return {
            "balance": {
                "XXBT": "1.0",  # 1 Bitcoin
                "XETH": "10.0",  # 10 Ethereum
                "ZUSD": "130.0"  # 130 USD
            },
            "open_positions": {},
            "open_orders": {},
            "order_history": {}
        }

    def _generate_txid(self) -> str:
        """Generate a unique transaction ID similar to Kraken's format."""
        return "T" + uuid.uuid4().hex[:9]

    def _get_current_price(self, pair: str) -> Decimal:
        """Get the current price for a trading pair."""
        return Decimal(self.prices.get(pair, "1.0"))

    def handle_request(self, uri_path: str, request_data: dict[str, Any]) -> dict[str, Any]:
        """Process a request based on the URI path."""
        if uri_path == '/0/private/Balance':
            return self._get_account_balance()
        elif uri_path == '/0/private/OpenPositions':
            return self._get_open_positions()
        elif uri_path == '/0/private/AddOrder':
            return self._add_order(request_data)
        elif uri_path == '/0/private/QueryOrders':
            return self._query_orders_info(request_data)

        return {
            "error": ["Simulator: Endpoint not implemented"],
            "result": {}
        }

    def _get_account_balance(self) -> dict[str, Any]:
        """Simulate the account balance endpoint."""
        return {
            "error": [],
            "result": self.simulator_data["balance"]
        }

    def _get_open_positions(self) -> dict[str, Any]:
        """Simulate the open positions endpoint."""
        return {
            "error": [],
            "result": self.simulator_data["open_positions"]
        }

    def _add_order(self, order_data: dict[str, Any]) -> dict[str, Any]:
        """Simulate adding an order."""
        pair = order_data.get("pair", "")
        order_type = order_data.get("type", "")  # buy or sell
        order_style = order_data.get("ordertype", "")  # market, limit, etc.
        volume = Decimal(order_data.get("volume", "0.0"))
        price = Decimal(order_data.get("price", "0.0")) if order_data.get(
            "price") else self._get_current_price(pair)

        # Generate a unique transaction ID
        txid = self._generate_txid()

        # Create the order
        order = {
            "txid": txid,
            "pair": pair,
            "type": order_type,
            "ordertype": order_style,
            "price": str(price),
            "volume": str(volume),
            "status": "open",
            "opentm": time.time(),  # Record when the order was created
            "closetm": 0
        }

        # For market orders, execute immediately
        if order_style == "market":
            self._execute_order(order)
            self.simulator_data["order_history"][txid] = order
        else:
            # For limit orders, add to open orders
            self.simulator_data["open_orders"][txid] = order

        return {
            "error": [],
            "result": {
                "txid": [txid],
                "descr": {
                    "order": f"{order_type} {volume} {pair} @ {price}"
                }
            }
        }

    def _execute_order(self, order: dict[str, Any]) -> None:
        """Simulate the execution of an order."""
        pair = order["pair"]
        order_type = order["type"]
        volume = Decimal(order["volume"])
        price = Decimal(order["price"])

        # Parse the trading pair to get the base and quote assets
        base_asset = 'X' + pair[:3]  # e.g., "XXBT"
        quote_asset = 'Z' + pair[3:]  # e.g., "ZUSD"

        # Calculate the total cost in quote currency
        cost = volume * price

        # Update balances based on the trade
        if order_type == "buy":
            quote_balance = Decimal(
                self.simulator_data["balance"].get(quote_asset, "0.0"))
            if quote_balance >= cost:
                self.simulator_data["balance"][quote_asset] = str(
                    quote_balance - cost)
                base_balance = Decimal(
                    self.simulator_data["balance"].get(base_asset, "0.0"))
                self.simulator_data["balance"][base_asset] = str(
                    base_balance + volume)
            else:
                raise ValueError(
                    f"Insufficient {quote_asset} balance for buy order")
        else:  # sell
            base_balance = Decimal(
                self.simulator_data["balance"].get(base_asset, "0.0"))
            if base_balance >= volume:
                self.simulator_data["balance"][base_asset] = str(
                    base_balance - volume)
                quote_balance = Decimal(
                    self.simulator_data["balance"].get(quote_asset, "0.0"))
                self.simulator_data["balance"][quote_asset] = str(
                    quote_balance + cost)
            else:
                raise ValueError(
                    f"Insufficient {base_asset} balance for sell order")

        # Update the order status
        order["status"] = "closed"
        order["closetm"] = time.time()

    def _query_orders_info(self, request_data: dict[str, Any]) -> dict[str, Any]:
        """Simulate querying order information, filling open orders if delay has passed."""
        txid = request_data.get("txid", "")
        result = {}

        # Check open orders and fill if time elapsed exceeds fill_delay
        if txid in self.simulator_data["open_orders"]:
            order = self.simulator_data["open_orders"][txid]
            current_time = time.time()
            time_elapsed = current_time - order["opentm"]

            if time_elapsed >= self.fill_delay:
                # Fill the order
                self._execute_order(order)
                self.simulator_data["order_history"][txid] = order
                del self.simulator_data["open_orders"][txid]
                result[txid] = order
            else:
                # Order still open
                result[txid] = order
            return {"error": [], "result": result}

        # Check order history
        order = self.simulator_data["order_history"].get(txid)
        if order:
            result[txid] = order
            return {"error": [], "result": result}

        # Order not found
        return {
            "error": ["Order not found"],
            "result": {}
        }

    def save_state(self, filename: str) -> None:
        """Save the current state of the simulator to a file.

        Args:
            filename: The name of the file to save the state to
        """
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.simulator_data, f, indent=2)
