import pandas as pd
import datetime as dt
import requests
import time

# Kraken API endpoint and pair
url_get_trades = "https://api.kraken.com/0/public/Trades"
symbol = "SOLUSD"

# Define the time range
start_dt = dt.datetime(2025, 1, 1, 0, 0, 0)
end_dt = dt.datetime(2025, 2, 13, 15, 0, 0)

start_ts = int(start_dt.timestamp())
end_ts = int(end_dt.timestamp())

trades_list = []
last_timestamp = start_ts

print(f"Fetching SOLUSD trades from {start_dt} to {end_dt}...")

while last_timestamp < end_ts:
    try:
        res = requests.get(url_get_trades, params={
                           "pair": symbol, "since": last_timestamp})
        response_data = res.json()

        if 'error' in response_data and response_data['error']:
            print("API error:", response_data['error'])
            time.sleep(5)
            continue

        symbol_internal = list(response_data["result"].keys())[0]
        trades = response_data["result"][symbol_internal]

        if not trades:
            break

        # Convert to DataFrame
        ticks = pd.DataFrame(trades, columns=[
                             "price", "volume", "time", "side", "orderType", "misc", "tradeID"])
        ticks['time'] = pd.to_datetime(ticks['time'], unit='s')
        ticks.set_index('time', inplace=True)

        # Filter for the desired time range
        ticks = ticks[ticks.index <= pd.to_datetime(end_ts, unit='s')]
        if ticks.empty:
            break

        trades_list.append(ticks)

        # Update last_timestamp with the newest trade's timestamp + 1 second
        last_timestamp = int(ticks.index[-1].timestamp()) + 1

        # Respect rate limits
        time.sleep(2)

    except Exception as e:
        print(f"Error occurred: {e}")
        time.sleep(5)
        continue

# Combine and clean up
if trades_list:
    trades_df = pd.concat(trades_list)
    trades_df.drop_duplicates(inplace=True)
    trades_df = trades_df.astype({"price": float, "volume": float})

    # Create OHLCV data
    ohlcv = trades_df['price'].resample('1H').ohlc()
    ohlcv['volume'] = trades_df['volume'].resample('1H').sum()
    ohlcv.dropna(inplace=True)

    print(ohlcv.head())
    print(f"\nGenerated {len(ohlcv)} hourly OHLC bars.")

    # Save to CSV with datetime index
    ohlcv.to_csv("solusd_1h_2025.csv")
else:
    print("No trades data was fetched.")
