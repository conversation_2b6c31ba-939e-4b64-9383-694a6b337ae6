import pandas as pd
import logging
import numpy as np
import math
from abc import ABC, abstractmethod
from app.trade_manager.trade_manager import TradeManager
from app.trade_manager.portfolio_manager import PortfolioManager


logger = logging.getLogger(__name__)


class BaseStrategy(ABC):
    """Abstract class for all trading strategies, handling trade execution, TP, SL, and trailing stops."""

    def __init__(self, pair: str, min_investment: float = 250, max_investment: float = 7500,
                 taker_maker_fee: float = 0.0026, interval: str = '1h',
                 take_profit_levels: list = [0.1, 0.15, 0.2], stop_loss: float = -0.1,
                 position_size_multiplier: float = 20.0, volatility_multiplier: float = 3.0):
        self.pair = pair
        self.min_investment = min_investment
        self.max_investment = max_investment
        self.taker_maker_fee = taker_maker_fee
        self.interval = interval
        self.take_profit_levels = take_profit_levels
        self.stop_loss = stop_loss
        self.portfolio_peak = None
        self.is_in_drawdown = False
        self.consecutive_losses = 0
        self._trade_manager = None
        self.market_regime = 0
        self.volatility = 0.02
        self.returns_window = []  # Store returns for Kelly Criterion calculation
        self.previous_net_worth = 0
        self.window_size = 20  # Size of the window for Kelly Criterion calculation
        self.net_worth_history = []
        self.last_sction = 0
        self.close_prices = []
        self.position_size_multiplier = position_size_multiplier
        self.volatility_multiplier = volatility_multiplier

    @abstractmethod
    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepares data before running the strategy."""
        pass

    @abstractmethod
    def should_enter_trade(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series) -> bool:
        """Determines if a new trade should be opened."""
        pass

    @abstractmethod
    def should_exit_trade(self, candle: pd.Series, previous_candle: pd.Series) -> bool:
        """Determines if an open trade should be closed."""
        pass

    def calculate_orderbook_features(self, orderbook_data: dict) -> dict:
        """
        Default implementation returns empty features.
        Override in subclass if needed.
        """
        return {
            'ob_spread': 0.0,
            'ob_spread_pct': 0.0,
            'ob_mid_price': 0.0,
            'ob_volume_imbalance': 0.0,
            'ob_bid_depth_5': 0.0,
            'ob_ask_depth_5': 0.0,
            'ob_total_bid_vol': 0.0,
            'ob_total_ask_vol': 0.0,
            'ob_bid_impact': 0.0,
            'ob_ask_impact': 0.0,
            'ob_weighted_bid': 0.0,
            'ob_weighted_ask': 0.0
        }

    def process_candle(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series, trade_manager: TradeManager, portfolio_manager: PortfolioManager):
        """Processes each new candle for trading decisions and trade management."""

        logging.info(f"Processing candle at {candle['timestamp']}")
        if hasattr(trade_manager, 'trade_history') and trade_manager.trade_history is not None:
            self.consecutive_losses = trade_manager.trade_history.consecutive_losses
        else:
            self.consecutive_losses = 0
        self._trade_manager = trade_manager
        self.close_prices.append(candle['close_price'])
        if len(self.close_prices) > self.window_size + 1:
            self.close_prices = self.close_prices[-(self.window_size + 1):]

        # Update volatility
        self.volatility = self.calculate_volatility()

        # **Manage open trades (Take Profits, Stop Loss, Trailing Stop)**
        logging.info("Managing active trades")
        self.manage_active_trades(candle, previous_candle, trade_manager)

        current_total_value = portfolio_manager.get_current_total_value(
            candle["close_price"])
        logging.info(f"Current total portfolio value: {current_total_value}")

        step_return = (current_total_value - self.previous_net_worth) / \
            self.previous_net_worth if self.previous_net_worth > 0 else 0

        # Store in returns window for metric calculations
        self.returns_window.append(step_return)
        if len(self.returns_window) > self.window_size:
            self.returns_window.pop(0)

        # Initialize portfolio_peak on first valid candle
        if self.portfolio_peak is None:
            logging.info("Initializing portfolio peak")
            self.portfolio_peak = current_total_value

        # Update portfolio peak if current value is higher
        if current_total_value > self.portfolio_peak:
            logging.info(f"New portfolio peak: {current_total_value}")
            self.portfolio_peak = current_total_value

        # Set drawdown flag if current value < 90% of peak
        self.is_in_drawdown = current_total_value < 0.9 * self.portfolio_peak
        logging.info(f"Is in drawdown: {self.is_in_drawdown}")

        # **Check for new trade entry**
        if self.should_enter_trade(candle, previous_candle, minusthree_candle) and portfolio_manager.current_balance > self.min_investment:
            logging.info("Conditions met for potential trade entry")
            if self.should_execute_trade(candle["timestamp"]):
                logging.info("Executing trade")
                self.execute_trade(candle, trade_manager, portfolio_manager)

        # **Update portfolio value at each step**
        logging.info("Recording daily portfolio value")
        portfolio_manager.record_daily_value(
            candle["timestamp"], candle["close_price"])
        self.previous_net_worth = current_total_value

    def manage_active_trades(self, candle: pd.Series, previous_candle: pd.Series, trade_manager: TradeManager):
        active_trade_ids = list(trade_manager.active_trades.keys())

        for trade_id in active_trade_ids:
            if trade_id not in trade_manager.active_trades:
                continue

            trade = trade_manager.active_trades[trade_id]
            sell_action = self.should_exit_trade(candle, previous_candle)

            # --------------------------------------------
            # 1. Handle Take Profits FIRST
            # --------------------------------------------
            for i, target in enumerate(self.get_take_profit_levels()):
                if (
                    not trade.reached_profit_levels[i] and
                    self.check_take_profit(trade.entry_price, candle["close_price"], target) and
                    not sell_action
                ):
                    exit_price = candle["close_price"]
                    # 25% per TP level
                    exit_volume = round(trade.initial_volume * 0.25, 5)
                    if exit_volume > trade.remaining_volume:
                        exit_volume = trade.remaining_volume

                    fee = round(exit_price * exit_volume *
                                self.taker_maker_fee, 5)
                    trade_manager.partial_exit(
                        trade_id=trade_id,
                        exit_price=exit_price,
                        exit_volume=exit_volume,
                        timestamp=candle["timestamp"],
                        fee=fee,
                        exit_reason=f'take_profit_{target*100:.1f}%'
                    )
                    trade.reached_profit_levels[i] = True

                    # Enable trailing stop after last TP
                    # if i == len(self.get_take_profit_levels()) - 1:
                    trade.trailing_stop_on = True

            # --------------------------------------------
            # 2. Handle Trailing Stop
            # --------------------------------------------
            if trade.trailing_stop_on:
                if candle["close_price"] > trade.max_price:
                    trade.max_price = candle["close_price"]
                    trade.trailing_price = trade.max_price * (
                        1 + (self.get_stop_loss() * 0.25 * len(trade.reached_profit_levels)))

            # --------------------------------------------
            # 3. Handle Stop Loss/Trailing Stop
            # --------------------------------------------
            if (
                (self.check_stop_loss(trade.entry_price, candle["close_price"]) or
                 candle["close_price"] <= trade.trailing_price) and
                not sell_action
            ):
                exit_volume = trade.remaining_volume
                fee = round(candle["close_price"] *
                            exit_volume * self.taker_maker_fee, 5)
                trade_manager.complete_exit(
                    trade_id=trade_id,
                    exit_price=candle["close_price"],
                    timestamp=candle["timestamp"],
                    fee=fee,
                    exit_reason="stop_loss" if not trade.trailing_stop_on else "trailing_stop_loss"
                )
                if not trade.trailing_stop_on:
                    self.cooldown_candles = 5

            # --------------------------------------------
            # 4. Handle Technical Exit LAST (only if trade still exists)
            # --------------------------------------------
            if (
                trade_id in trade_manager.active_trades and
                sell_action
            ):
                fee = round(
                    candle["close_price"] * trade.remaining_volume * self.taker_maker_fee, 5)
                trade_manager.complete_exit(
                    trade_id=trade_id,
                    exit_price=candle["close_price"],
                    timestamp=candle["timestamp"],
                    fee=fee,
                    exit_reason="exit_signal"
                )

    def execute_trade(self, candle: pd.Series, trade_manager: TradeManager, portfolio_manager: PortfolioManager):
        """Executes a new trade based on entry signal."""
        entry_price = float(candle["close_price"])
        entry_time = candle["timestamp"]

        # Calculate optimal position size based on Kelly Criterion
        # This dynamically adjusts trade size based on account balance and market conditions
        risk_percentage = self.calculate_kelly_fraction()

        # Volatility adjustment - reduce position size in high volatility
        if hasattr(self, 'volatility') and self.volatility > 0:
            # Assuming volatility is calculated elsewhere (e.g., rolling standard deviation of returns)
            volatility = float(self.volatility)
            val = float(risk_percentage) / (volatility * float(self.volatility_multiplier))
            if val < 0.02:
                risk_percentage = 0.02
            elif val > 0.1:
                risk_percentage = 0.1
            else:
                risk_percentage = val

        # Maximum position size
        investment_amount = portfolio_manager.current_balance * \
            risk_percentage * self.position_size_multiplier

        if self.is_in_drawdown:
            investment_amount *= 0.8

        # Calculate total currently invested (sum of all open trades' invested amounts)
        total_invested = portfolio_manager.reserved_funds
        available_to_invest = max(0, self.max_investment - total_invested)
        investment_amount = min(investment_amount, available_to_invest)
        if investment_amount < self.min_investment:
            return  # Skip trade if not enough room to invest at least min_investment

        fee = round(investment_amount * self.taker_maker_fee, 5)

        total_cost = investment_amount + fee
        # Ensure sufficient funds
        if portfolio_manager.current_balance < total_cost:
            return  # Skip trade if insufficient funds

        volume = round(investment_amount / entry_price, 5)

        trade_manager.open_trade(
            price=entry_price,
            volume=volume,
            timestamp=entry_time,
            pair=self.pair,
            fee=fee,
            strategy_name=self.__class__.__name__,
            entry_reason="strategy_entry",
            take_profit_levels=self.get_take_profit_levels()
        )

    def get_take_profit_levels(self) -> list:
        return self.take_profit_levels

    def get_stop_loss(self) -> float:
        return self.stop_loss

    def check_take_profit(self, entry_price: float, current_price: float, target: float) -> bool:
        return ((current_price - entry_price) / entry_price) >= target

    def check_stop_loss(self, entry_price: float, current_price: float) -> bool:
        return ((current_price - entry_price) / entry_price) <= self.get_stop_loss()

    def resample_data(self, df: pd.DataFrame) -> pd.DataFrame:
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')

        # Define resampling rules for OHLCV data
        resampled = df.resample(self.interval).agg({
            'open_price': 'first',
            'high_price': 'max',
            'low_price': 'min',
            'close_price': 'last',
            'volume': 'sum'
        })

        resampled = resampled.reset_index()
        resampled = resampled.dropna()
        return resampled

    def should_execute_trade(self, timestamp):
        """
        Determines whether a trade should be executed based on the time and day.
        """
        dt = timestamp  # Convert to datetime
        hour = dt.hour
        weekday = dt.weekday()  # Monday = 0, Sunday = 6
        return True

        # Avoid trading on Fridays (weekday == 4) or during the worst hour (2 PM)
        # if weekday == 4 or hour == 14:
        #    return False  # Skip trading

        # Prioritize best hour (4 AM) or best day (Wednesday - weekday == 2)
        # if hour == 4 or weekday == 2:
        #    return True  # Strong preference to trade

        # return True  # Allow normal trading outside excluded times

    def calculate_kelly_fraction(self):
        """Calculate optimal position size using the Kelly Criterion with robust safeguards"""
        # We need sufficient history to calculate win rate and average returns
        if not hasattr(self, 'returns_window') or len(self.returns_window) < 10:
            return 0.02  # Default to 2% when we don't have enough data

        # Calculate win probability from historical returns
        wins = sum(1 for r in self.returns_window if r > 0)
        win_probability = wins / len(self.returns_window)

        # If win probability is too low, be conservative
        if win_probability < 0.4:
            return 0.01  # Very small position size when win rate is poor

        # Calculate average win and loss sizes
        win_returns = [r for r in self.returns_window if r > 0]
        loss_returns = [abs(r) for r in self.returns_window if r < 0]

        # Safeguard against empty lists
        avg_win = sum(win_returns) / len(win_returns) if win_returns else 0.01
        avg_loss = sum(loss_returns) / \
            len(loss_returns) if loss_returns else 0.01

        # Ensure we don't divide by zero
        if avg_loss <= 0:
            avg_loss = 0.01

        # Calculate win/loss ratio
        win_loss_ratio = avg_win / avg_loss

        # Kelly formula: f* = (p × b - q) / b
        # where p is win probability, q is loss probability (1-p),
        # and b is the win/loss ratio
        kelly_fraction = (win_probability * win_loss_ratio -
                          (1 - win_probability)) / win_loss_ratio

        # Negative Kelly means don't trade
        if kelly_fraction <= 0:
            return 0.01  # Minimum position size

        # Apply constraints to the Kelly fraction
        # - Half-Kelly is often used in practice to reduce volatility
        # - Set lower and upper bounds for safety
        half_kelly = kelly_fraction * 0.5
        bounded_kelly = max(0.01, min(0.2, half_kelly))  # Between 1% and 20%

        # Further adjust based on market conditions
        if hasattr(self, 'volatility') and self.volatility > 0:
            # Reduce position size in high volatility environments
            volatility_adjustment = 1.0 / (1.0 + self.volatility * 10)
            bounded_kelly *= volatility_adjustment

        # If market regime is bearish, further reduce position size
        if hasattr(self, 'market_regime') and self.market_regime < 0:
            bounded_kelly *= 0.5

        # Add drawdown adjustment - if recent drawdown, be more conservative
        if hasattr(self, 'net_worth_history') and len(self.net_worth_history) > 10:
            recent_peak = max(self.net_worth_history[-10:])
            current_value = self.net_worth_history[-1]
            drawdown = 1 - (current_value /
                            recent_peak) if recent_peak > 0 else 0

            if drawdown > 0.05:  # More than 5% drawdown
                bounded_kelly *= (1 - min(0.8, drawdown * 4)
                                  )  # Reduce by up to 80%

        # Minimum position size is 1%
        return float(max(0.01, bounded_kelly))

    def calculate_volatility(self, window_size=20):

        if len(self.close_prices) < 2:
            return 0.02  # Default value if not enough data

        # Use the stored close prices
        # Last 'window_size' prices
        price_window = self.close_prices[-window_size:]

        # Calculate returns
        if len(price_window) < 2:
            return 0.02

        returns = np.diff(price_window) / price_window[:-1]

        # Calculate volatility (standard deviation of returns)
        volatility = np.std(returns)

        # Annualize based on interval
        if self.interval == '1h':
            volatility *= math.sqrt(24 * 365)
        elif self.interval == '1d':
            volatility *= math.sqrt(365)

        # Clamp between 1% and 20%
        volatility = max(0.01, min(0.2, float(volatility)))

        return volatility

