import sys
import os
import requests
import time
import urllib.parse
import hashlib
import hmac
import base64
import threading
import logging
from typing import Any, Optional
project_root = os.path.abspath(os.path.join(
    os.path.dirname(__file__), "..", ".."))
sys.path.append(project_root)

logger = logging.getLogger(__name__)


class KrakenAPI:
    def __init__(self, api_key: str, api_secret: str, api_url: str, use_simulator: bool = False, simulator_data_file: Optional[str] = None, default_pair: str = 'XBTUSD'):
        self.api_key = api_key
        self.api_secret = api_secret
        self.api_url = api_url
        self.use_simulator = use_simulator
        self.default_pair = default_pair
        self.last_nonce = int(1000000 * time.time())
        self.nonce_lock = threading.Lock()

        # Initialize simulator if needed
        if use_simulator:
            from app.exchange.kraken_simulator import KrakenSimulator
            # Provide a default empty string if simulator_data_file is None to satisfy type requirement
            self.simulator = KrakenSimulator(simulator_data_file or "")

    def _get_nonce(self):
        with self.nonce_lock:
            nonce = int(1000000 * time.time())
            if nonce <= self.last_nonce:
                nonce = self.last_nonce + 1
            self.last_nonce = nonce
            return str(nonce)

    def _get_kraken_signature(self, urlpath: str, request_data: dict[str, Any], secret: str) -> str:
        postdata = urllib.parse.urlencode(request_data)
        encoded = (request_data['nonce'] + postdata).encode()
        message = urlpath.encode() + hashlib.sha256(encoded).digest()
        mac = hmac.new(base64.b64decode(secret), message, hashlib.sha512)
        sigdigest = base64.b64encode(mac.digest())
        return sigdigest.decode()

    def _kraken_request(self, uri_path: str, request_params: dict[str, Any], auth: bool = False) -> dict[str, Any]:
        # If using simulator for authenticated endpoints
        if self.use_simulator and auth:
            ret = self.simulator.handle_request(uri_path, request_params)
            if ret['error'] != "Simulator: Endpoint not implemented":
                return ret

        # Otherwise, use the real API
        if auth:
            request_params['nonce'] = self._get_nonce()
            headers = {
                'API-Key': self.api_key,
                'API-Sign': self._get_kraken_signature(uri_path, request_params, self.api_secret)
            }
        else:
            headers = {}
        try:
            response = requests.post(
                f"{self.api_url}{uri_path}", headers=headers, data=request_params)
            result = response.json()

            if result.get("error"):
                logger.error(
                    f"API returned error: {result['error']} | Endpoint: {uri_path} | Request: {request_params}")

            return result

        except ValueError as e:
            logger.exception(f"Failed to parse JSON from API response: {e}")
            raise
        except requests.RequestException as e:
            logger.exception(f"Request failed: {e}")
            raise

    # Public Endpoints

    def get_server_time(self) -> dict[str, Any]:
        return self._kraken_request('/0/public/Time', {})

    def get_system_status(self) -> dict[str, Any]:
        return self._kraken_request('/0/public/SystemStatus', {})

    # Removed duplicate methods that are redefined below with more complete implementations

    def get_ohlc_data(self, pair: str, interval: int = 1, since: int = int(time.time() - 86400) * 1000000000) -> dict[str, Any]:
        return self._kraken_request('/0/public/OHLC', {'pair': pair, 'interval': str(interval), 'since': str(since)})

    # Removed duplicate methods that are redefined below with more complete implementations

    def get_assets(self, asset: Optional[str] = None, aclass: Optional[str] = None) -> dict[str, Any]:
        """Get information about the assets that are available for deposit, withdrawal, trading and staking."""
        params = {}
        if asset is not None:
            params['asset'] = str(asset)
        if aclass is not None:
            params['aclass'] = str(aclass)
        return self._kraken_request('/0/public/Assets', params)

    def get_asset_pairs(self, pair: Optional[str] = None, info_type: Optional[str] = None) -> dict[str, Any]:
        """Get tradable asset pairs.

        Args:
            pair: Optional trading pair to filter by
            info_type: Optional info type to return (e.g., 'leverage', 'fees', 'margin')

        Returns:
            Dictionary containing asset pairs information
        """
        params = {}
        if pair:
            params['pair'] = pair
        if info_type:
            params['info'] = info_type
        return self._kraken_request('/0/public/AssetPairs', params)

    def get_ticker(self, pair: str) -> dict[str, Any]:
        """Get ticker information."""
        return self._kraken_request('/0/public/Ticker', {'pair': pair})

    def get_ohlc(self, pair: str, interval: Optional[int] = None, since: Optional[int] = None) -> dict[str, Any]:
        """Get OHLC data."""
        params = {'pair': pair}
        if interval:
            params['interval'] = str(interval)
        if since:
            params['since'] = str(since)
        return self._kraken_request('/0/public/OHLC', params)

    def get_order_book(self, pair: str, count: Optional[int] = None) -> dict[str, Any]:
        """Get order book."""
        params = {'pair': pair}
        if count:
            params['count'] = str(count)
        return self._kraken_request('/0/public/Depth', params)

    def get_recent_trades(self, pair: str, since: Optional[int] = None, count: Optional[int] = None) -> dict[str, Any]:
        """Get recent trades."""
        params = {'pair': pair}
        if since:
            params['since'] = str(since)
        if count:
            params['count'] = str(count)
        return self._kraken_request('/0/public/Trades', params)

    def get_recent_spreads(self, pair: str, since: Optional[int] = None) -> dict[str, Any]:
        """Get recent spreads."""
        params = {'pair': pair}
        if since:
            params['since'] = str(since)
        return self._kraken_request('/0/public/Spread', params)

    def get_tradable_asset_pairs_price_tickers(self, pair: Optional[list[str]] = None) -> dict[str, Any]:
        """Get tradable asset pair prices."""
        params = {}
        if pair:
            params['pair'] = ','.join(pair)
        return self._kraken_request('/0/public/Ticker', params)

    def get_tradable_asset_pairs_ohlc_data(self, pair: str, interval: Optional[int] = None, since: Optional[int] = None) -> dict[str, Any]:
        """Get tradable asset pair OHLC data."""
        params = {'pair': pair}
        if interval:
            params['interval'] = str(interval)
        if since:
            params['since'] = str(since)
        return self._kraken_request('/0/public/OHLC', params)

    def get_tradable_asset_pairs_order_book(self, pair: str, count: Optional[int] = None) -> dict[str, Any]:
        """Get tradable asset pair order book."""
        params = {'pair': pair}
        if count:
            params['count'] = str(count)
        return self._kraken_request('/0/public/Depth', params)

    def get_tradable_asset_pairs_last_trade_data(self, pair: str, since: Optional[int] = None, count: Optional[int] = None) -> dict[str, Any]:
        """Get tradable asset pair last trade data."""
        params = {'pair': pair}
        if since:
            params['since'] = str(since)
        if count:
            params['count'] = str(count)
        return self._kraken_request('/0/public/Trades', params)

    def get_tradable_asset_pairs_spread_data(self, pair: str, since: Optional[int] = None) -> dict[str, Any]:
        """Get tradable asset pair spread data."""
        params = {'pair': pair}
        if since:
            params['since'] = str(since)
        return self._kraken_request('/0/public/Spread', params)

    # Private Endpoints

    def get_account_balance(self) -> dict[str, Any]:
        return self._kraken_request('/0/private/Balance', {}, auth=True)

    def get_trade_balance(self, asset: Optional[str] = None) -> dict[str, Any]:
        """Get trade balance for a specific asset.

        Args:
            asset: Optional asset to get balance for (e.g., 'XXBT' for Bitcoin)

        Returns:
            Dictionary containing trade balance information
        """
        params = {}
        if asset is not None:
            params['asset'] = str(asset)
        return self._kraken_request('/0/private/TradeBalance', params, auth=True)

    def get_open_orders(self, trades: str = 'false', userref: Optional[int] = None) -> dict[str, Any]:
        """Get all open orders.

        Args:
            trades: Whether to include trades in output
            userref: Restrict results to given user reference ID

        Returns:
            Dictionary containing open orders information
        """
        params = {'trades': trades}
        if userref is not None:
            params['userref'] = str(userref)
        return self._kraken_request('/0/private/OpenOrders', params, auth=True)

    def get_closed_orders(self, trades: bool = False, userref: Optional[int] = None, start: Optional[str] = None, end: Optional[str] = None, ofs: Optional[str] = None, closetime: str = 'both') -> dict[str, Any]:
        """Get closed orders.

        Args:
            trades: Whether to include trades in output
            userref: Restrict results to given user reference ID
            start: Starting timestamp or order transaction ID
            end: Ending timestamp or order transaction ID
            ofs: Result offset for pagination
            closetime: Which time to use for closed orders ('open', 'close', or 'both')

        Returns:
            Dictionary containing closed orders information
        """
        params = {'trades': trades, 'closetime': closetime}
        if userref is not None:
            params['userref'] = str(userref)
        if start is not None:
            params['start'] = str(start)
        if end is not None:
            params['end'] = str(end)
        if ofs is not None:
            params['ofs'] = str(ofs)
        return self._kraken_request('/0/private/ClosedOrders', params, auth=True)

    def query_orders_info(self, txid: str, trades: bool = False, userref: Optional[int] = None) -> dict[str, Any]:
        """Query information about specific orders.

        Args:
            txid: Comma-delimited list of transaction IDs to query about
            trades: Whether to include trades in output
            userref: Restrict results to given user reference ID

        Returns:
            Dictionary containing order information
        """
        params = {'txid': txid, 'trades': trades}
        if userref is not None:
            params['userref'] = str(userref)
        return self._kraken_request('/0/private/QueryOrders', params, auth=True)

    def get_trades_history(self, trade_type: str = 'all', trades: bool = False, start: Optional[str] = None, end: Optional[str] = None, ofs: Optional[str] = None) -> dict[str, Any]:
        """Get trades history.

        Args:
            trade_type: Type of trade ('all', 'any position', 'closed position', 'closing position', 'no position')
            trades: Whether to include trades in output
            start: Starting timestamp or trade ID
            end: Ending timestamp or trade ID
            ofs: Result offset for pagination

        Returns:
            Dictionary containing trades history
        """
        params = {'type': trade_type, 'trades': trades}
        if start:
            params['start'] = start
        if end:
            params['end'] = end
        if ofs:
            params['ofs'] = ofs
        return self._kraken_request('/0/private/TradesHistory', params, auth=True)

    def query_trades_info(self, txid: str, trades: bool = False) -> dict[str, Any]:
        return self._kraken_request('/0/private/QueryTrades', {'txid': txid, 'trades': trades}, auth=True)

    def get_open_positions(self, txid: Optional[str] = None, docalcs: bool = False, consolidation: str = 'market') -> dict[str, Any]:
        """Get open positions.

        Args:
            txid: Filter by transaction ID
            docalcs: Whether to include P&L calculations
            consolidation: Type of consolidation to use ('market' or 'trade')

        Returns:
            Dictionary containing open positions information
        """
        params = {'docalcs': docalcs, 'consolidation': consolidation}
        if txid:
            params['txid'] = txid
        return self._kraken_request('/0/private/OpenPositions', params, auth=True)

    def get_ledgers_info(self, asset: Optional[str] = None, aclass: str = 'currency', ledger_type: str = 'all', start: Optional[str] = None, end: Optional[str] = None, ofs: Optional[str] = None) -> dict[str, Any]:
        """Get ledgers information.

        Args:
            asset: Asset to get ledger info for
            aclass: Asset class ('currency' by default)
            ledger_type: Type of ledger to retrieve ('all', 'deposit', 'withdrawal', 'trade', 'margin')
            start: Starting timestamp or ledger ID
            end: Ending timestamp or ledger ID
            ofs: Result offset for pagination

        Returns:
            Dictionary containing ledgers information
        """
        params = {'aclass': aclass, 'type': ledger_type}
        if asset:
            params['asset'] = asset
        if start:
            params['start'] = start
        if end:
            params['end'] = end
        if ofs:
            params['ofs'] = ofs
        return self._kraken_request('/0/private/Ledgers', params, auth=True)

    def query_ledgers(self, id: str) -> dict[str, Any]:
        return self._kraken_request('/0/private/QueryLedgers', {'id': id}, auth=True)

    def get_trade_volume(self, pair: Optional[str] = None, fee_info: str = 'true') -> dict[str, Any]:
        """Get trade volume information.

        Args:
            pair: Optional trading pair to filter by
            fee_info: Whether to include fee information

        Returns:
            Dictionary containing trade volume information
        """
        params = {'fee-info': fee_info}
        if pair:
            params['pair'] = pair
        return self._kraken_request('/0/private/TradeVolume', params, auth=True)

    def add_order(self, pair: str, order_type: str, ordertype: str, volume: str, price: Optional[str] = None, price2: Optional[str] = None, leverage: Optional[str] = None, oflags: Optional[str] = None, starttm: Optional[str] = None, expiretm: Optional[str] = None, userref: Optional[int] = None, validate: Optional[str] = None, close_order_type: Optional[str] = None, close_price: Optional[str] = None, close_price2: Optional[str] = None, trading_agreement: Optional[str] = None) -> dict[str, Any]:
        """Add a new order.

        Args:
            pair: Asset pair
            order_type: Type of order ('buy'/'sell')
            ordertype: Order type ('market'/'limit'/'stop-loss'/'take-profit'/'stop-loss-limit'/'take-profit-limit')
            volume: Order volume in lots
            price: Price (optional.  Dependent upon ordertype)
            price2: Secondary price (optional.  Dependent upon ordertype)
            leverage: Amount of leverage desired (optional.  default = none)
            oflags: Comma delimited list of order flags (optional)
            starttm: Scheduled start time (optional):
                - 0 = now (default)
                - +<n> = schedule start time <n> seconds from now
                - <n> = unix timestamp of start time
            expiretm: Expiration time (optional):
                - 0 = no expiration (default)
                - +<n> = expire <n> seconds from now
                - <n> = unix timestamp of expiration time
            userref: User reference id (optional)
            validate: Validate inputs only. Do not submit order (optional)
            close_order_type: Close order type (optional)
            close_price: Close price (optional)
            close_price2: Secondary close price (optional)
            trading_agreement: Agreement to trading terms (optional)

        Returns:
            Dictionary containing order information
        """
        data = {
            'pair': pair,
            'type': order_type,
            'ordertype': ordertype,
            'volume': volume
        }
        if price is not None:
            data['price'] = str(price)
        if price2 is not None:
            data['price2'] = str(price2)
        if leverage is not None:
            data['leverage'] = str(leverage)
        if oflags is not None:
            data['oflags'] = str(oflags)
        if starttm is not None:
            data['starttm'] = str(starttm)
        if expiretm is not None:
            data['expiretm'] = str(expiretm)
        if userref is not None:
            data['userref'] = str(userref)
        if validate is not None:
            data['validate'] = validate
        if close_order_type is not None:
            data['close[ordertype]'] = str(close_order_type)
        if close_price is not None:
            data['close[price]'] = str(close_price)
        if close_price2 is not None:
            data['close[price2]'] = str(close_price2)
        if trading_agreement is not None:
            data['trading_agreement'] = str(trading_agreement)
        return self._kraken_request('/0/private/AddOrder', data, auth=True)

    def cancel_order(self, txid: str) -> dict[str, Any]:
        return self._kraken_request('/0/private/CancelOrder', {'txid': txid}, auth=True)

    def cancel_all_orders(self) -> dict[str, Any]:
        return self._kraken_request('/0/private/CancelAll', {}, auth=True)

    def cancel_all_orders_after_x(self, timeout: int) -> dict[str, Any]:
        return self._kraken_request('/0/private/CancelAllOrdersAfter', {'timeout': timeout}, auth=True)
