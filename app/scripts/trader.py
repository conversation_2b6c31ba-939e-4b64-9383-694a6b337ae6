import asyncio
# Removed unused import: import pandas as pd
from datetime import datetime
from app.strategy.base_strategy import BaseStrategy
from app.scripts.base_data_feed import DataFeed
from app.scripts.base_order_executor import OrderExecutor
from app.trade_manager.trade_manager import TradeManager
from app.trade_manager.portfolio_manager import PortfolioManager
import logging

logger = logging.getLogger(__name__)


class Trader:
    """Orchestrates the trading process for backtest, paper, or live modes."""

    def __init__(self, strategy: BaseStrategy, data_feed: DataFeed, order_executor: OrderExecutor,
                 trade_manager: TradeManager, portfolio_manager: PortfolioManager, mode: str,
                 order_monitor_interval: int = 5):
        """
        Initialize the Trader instance.

        Args:
            strategy: The trading strategy (e.g., EMAStrategy).
            data_feed: Source of candle data (historical or real-time).
            order_executor: Handles order placement (simulated or real).
            trade_manager: Manages active and pending trades.
            portfolio_manager: Tracks portfolio balance and performance.
            mode: 'backtest', 'paper', or 'live' to determine behavior.
            order_monitor_interval: Seconds between pending order checks in live mode.
        """
        self.strategy = strategy
        self.data_feed = data_feed
        self.order_executor = order_executor
        self.trade_manager = trade_manager
        self.portfolio_manager = portfolio_manager
        self.mode = mode
        self.order_monitor_interval = order_monitor_interval
        self.logger = logging.getLogger(__name__)
        self.last_candle = None
        self.previous_candle = None
        self._lock = asyncio.Lock()

    async def order_monitor(self):
        """Background task to monitor and update pending orders in live trading."""
        while True:
            async with self._lock:
                await asyncio.sleep(self.order_monitor_interval)
                current_time = datetime.now()
                filled_orders = self.order_executor.check_pending_orders(
                    current_time)
                self.trade_manager.process_pending_orders(filled_orders)
                if filled_orders:
                    self.logger.info(
                        f"Processed {len(filled_orders)} filled orders at {current_time}")

    async def run(self):
        """Execute the trading loop, processing candles and managing trades."""
        await self.data_feed.connect()

        # Start order monitoring task only for live trading
        order_monitor_task = None
        if self.mode == 'live':
            order_monitor_task = asyncio.create_task(self.order_monitor())

        try:
            self.logger.info(
                f"Starting {self.mode} trading for pair {self.strategy.pair}")
            while True:
                candle = await self.data_feed.get_next_candle()
                if candle is None:
                    self.logger.info(
                        f"No more candles to process in {self.mode} mode")
                    break

                # Process the candle with the strategy
                minusthree_candle = self.previous_candle
                self.previous_candle = self.last_candle

                # Make sure we have valid candles before processing
                if self.previous_candle is None:
                    self.previous_candle = candle
                if minusthree_candle is None:
                    minusthree_candle = candle

                self.strategy.process_candle(
                    candle, self.previous_candle, minusthree_candle, self.trade_manager, self.portfolio_manager
                )
                self.last_candle = candle

                # Log progress
                self.logger.info(f"Processed candle at {candle['timestamp']}")

        except Exception as e:
            self.logger.error(
                f"Error in {self.mode} trading: {e}", exc_info=True)
            raise
        finally:
            # Clean up
            if order_monitor_task:
                order_monitor_task.cancel()
                try:
                    await order_monitor_task
                except asyncio.CancelledError:
                    pass

            # Close active trades at the last price for backtest/paper modes
            if self.mode in ['backtest', 'paper'] and self.trade_manager.active_trades:
                if self.last_candle is not None:
                    exit_price = float(self.last_candle["close_price"])
                    for trade_id in list(self.trade_manager.active_trades.keys()):
                        trade = self.trade_manager.active_trades[trade_id]
                        # Example fee: 0.25%
                        fee = round(
                            exit_price * trade.remaining_volume * 0.0025, 2)
                        self.trade_manager.complete_exit(
                            trade_id, exit_price, self.last_candle["timestamp"], fee, 'end_of_session'
                        )
            self.logger.info(f"{self.mode.capitalize()} trading completed")
