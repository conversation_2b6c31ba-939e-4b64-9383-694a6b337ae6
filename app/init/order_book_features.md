# Order Book Feature Ideas for PPO Observations

## Price/Spread Features

### Bid-Ask Spread
The difference between the best (lowest) ask and best (highest) bid prices. It measures immediate liquidity and transaction cost – tighter spreads mean more liquidity. A small spread implies buyers and sellers are close (good liquidity), while a large spread indicates a thin book.  

**Calculation**:  
`Spread = BestAsk – BestBid` (or as a fraction of mid-price).

### Mid-Price (Equilibrium Price)
The midpoint of the best bid and ask. It represents the current fair market price implied by the order book and can serve as a reference level.  

**Calculation**:  
`MidPrice = (BestAsk + BestBid) / 2`.  

The mid-price is useful for measuring price moves (e.g., shifts above/below mid) and stabilizes noise by averaging the two sides.

---

## Liquidity and Depth Features

### Top-of-Book Volumes
The available volume at the best bid and best ask. These indicate the immediate supply (ask-side) and demand (bid-side) at the current prices. For example, unusually large bid-side volume (relative to ask) signals strong buying interest.  

**Calculation**: Just take the current `Volume_bid[1]` and `Volume_ask[1]` from level 1.

### Total Depth (Top N Liquidity)
The sum of volumes on each side across the top N levels. This measures how much quantity is available before price moves. High total depth means a very liquid book.  

**Calculation**: For some small N (e.g., 5–10), compute:  
`Depth_bid = ∑_{i=1..N} Volume_bid[i]`  
Similarly for `Depth_ask`.  

These totals (or their ratio) quantify overall liquidity. For example, a much larger `Depth_bid` than `Depth_ask` indicates excess buying interest (upward pressure).

### Depth Imbalance Ratio
The normalized difference between total bid-side and ask-side depth.  

**Calculation**:  
`Imbalance = (Depth_bid - Depth_ask) / (Depth_bid + Depth_ask)`  

Yielding a value in (–1, 1). Positive values signal net buy-side volume (buying pressure), negative values signal selling pressure. This scale-invariant metric highlights supply-demand tilt.  

**Why useful**: Large positive imbalance has been noted to precede short-term price rises, while negative values point to downward pressure.

### VWAP of Top N Levels
The volume-weighted average price of the orders in the top N levels on one side.  

**Calculation**:  
`VWAP_ask = (∑{i=1}^N Price_ask,i × Volume_ask,i) / (∑{i=1}^N Volume_ask,i)`

Similarly for bids.  

This reflects the average execution price if a market order consumed all top-N volume.  

**Why useful**: A large difference between VWAP and the best price indicates thin liquidity – e.g., if `VWAP_ask` is much higher than `BestAsk`, the ask side is light and a buy would push price up significantly. It directly quantifies price impact for a given trade size.

### Price Range/Book Width
The gap between the best price and a deeper price level. For instance, the difference between the 1st and 5th bid prices (or ask prices). A large gap or wide range means lower depth/fragile liquidity (few orders far from mid).  

**Calculation**:  
e.g., `PriceRange_bid = BidPrice[N] - BidPrice[1]` for some N.  

This captures “how far price must move” to absorb volume at the far edges of the book.

---

## Imbalance and Pressure Metrics

### Volume Delta (Net Volume Difference)
The net difference between total bid- and ask-side volumes in the book (often across top N levels).  

**Calculation**:  
`VD = (∑ bid_vol) – (∑ ask_vol)`  

**What it measures**: Raw supply-demand gap.  

**Why useful**: Positive VD means more demand (buy-side volume) than supply, suggesting upward pressure; negative means excess supply.  

*(Note: some sources define VD as `∑(ask – bid)`, but the interpretation is the same.)*

### Weighted Book Imbalance
A refinement that weights volumes by level distance. For example, weight level `i` by a decaying factor (e.g., `1/i` or exponential).  

**Why**: Volumes close to the mid (level 1) are more immediately actionable than those deep in the book.  

**Calculation**:  
e.g., `Imb_weighted = Σ_{i=1..N} w_i * (bid_vol[i] - ask_vol[i])`, with weights `w_i` decreasing.  

This yields a single imbalance score giving more importance to near-market supply/demand.

### Order Book Imbalance Ratio
Similar to total-depth imbalance but done per level or aggregated.  

**Calculation**:  
Similarly for bids.  

This reflects the average execution price if a market order consumed all top-N volume.  

**Why useful**: A large difference between VWAP and the best price indicates thin liquidity – e.g., if `VWAP_ask` is much higher than `BestAsk`, the ask side is light and a buy would push price up significantly. It directly quantifies price impact for a given trade size.

### Price Range/Book Width
The gap between the best price and a deeper price level. For instance, the difference between the 1st and 5th bid prices (or ask prices). A large gap or wide range means lower depth/fragile liquidity (few orders far from mid).  

**Calculation**:  
e.g., `PriceRange_bid = BidPrice[N] - BidPrice[1]` for some N.  

This captures “how far price must move” to absorb volume at the far edges of the book.

---

## Imbalance and Pressure Metrics

### Volume Delta (Net Volume Difference)
The net difference between total bid- and ask-side volumes in the book (often across top N levels).  

**Calculation**:  
`VD = (∑ bid_vol) – (∑ ask_vol)`  

**What it measures**: Raw supply-demand gap.  

**Why useful**: Positive VD means more demand (buy-side volume) than supply, suggesting upward pressure; negative means excess supply.  

*(Note: some sources define VD as `∑(ask – bid)`, but the interpretation is the same.)*

### Weighted Book Imbalance
A refinement that weights volumes by level distance. For example, weight level `i` by a decaying factor (e.g., `1/i` or exponential).  

**Why**: Volumes close to the mid (level 1) are more immediately actionable than those deep in the book.  

**Calculation**:  
e.g., `Imb_weighted = Σ_{i=1..N} w_i * (bid_vol[i] - ask_vol[i])`, with weights `w_i` decreasing.  

This yields a single imbalance score giving more importance to near-market supply/demand.

### Order Book Imbalance Ratio
Similar to total-depth imbalance but done per level or aggregated.  

**Calculation**:  Similarly for bids.  

This reflects the average execution price if a market order consumed all top-N volume.  

**Why useful**: A large difference between VWAP and the best price indicates thin liquidity – e.g., if `VWAP_ask` is much higher than `BestAsk`, the ask side is light and a buy would push price up significantly. It directly quantifies price impact for a given trade size.

### Price Range/Book Width
The gap between the best price and a deeper price level. For instance, the difference between the 1st and 5th bid prices (or ask prices). A large gap or wide range means lower depth/fragile liquidity (few orders far from mid).  

**Calculation**:  
e.g., `PriceRange_bid = BidPrice[N] - BidPrice[1]` for some N.  

This captures “how far price must move” to absorb volume at the far edges of the book.

---

## Imbalance and Pressure Metrics

### Volume Delta (Net Volume Difference)
The net difference between total bid- and ask-side volumes in the book (often across top N levels).  

**Calculation**:  
`VD = (∑ bid_vol) – (∑ ask_vol)`  

**What it measures**: Raw supply-demand gap.  

**Why useful**: Positive VD means more demand (buy-side volume) than supply, suggesting upward pressure; negative means excess supply.  

*(Note: some sources define VD as `∑(ask – bid)`, but the interpretation is the same.)*

### Weighted Book Imbalance
A refinement that weights volumes by level distance. For example, weight level `i` by a decaying factor (e.g., `1/i` or exponential).  

**Why**: Volumes close to the mid (level 1) are more immediately actionable than those deep in the book.  

**Calculation**:  
e.g., `Imb_weighted = Σ_{i=1..N} w_i * (bid_vol[i] - ask_vol[i])`, with weights `w_i` decreasing.  

This yields a single imbalance score giving more importance to near-market supply/demand.

### Order Book Imbalance Ratio
Similar to total-depth imbalance but done per level or aggregated.  

**Calculation**:  

`ImbRatio = (BidVol - AskVol) / (BidVol + AskVol)`

Which lies between –1 and 1.  

**What**: Positive = buying excess, negative = selling excess.  

**Why**: It’s normalized to liquidity; for example, this “provides a normalized measure of buying vs. selling pressure.” Traders use it to gauge short-term price pressure – e.g., a large positive imbalance often precedes rises.

### Cumulative Volume Delta (CVD) / Imbalance Momentum
The change in Volume Delta over recent time.  

**Calculation**: Compute `VD` at times `t−1` and `t` and take the difference.  

**What**: Captures how rapidly the net order imbalance is shifting.  

**Why**: Sudden surges (changes of sign) in CVD can flag an incoming strong move. For instance, a spike in net buy-volume accumulation often foreshadows an up-tick in price.

---

## Additional Features and Intuition

### Best-Level Volume Ratio
The ratio of volume at best bid to volume at best ask.  

**Calculation**:  
`Vol_bid[1] / Vol_ask[1]` or difference.  

**Why useful**: It’s very quick to compute and often indicates who “wins” at the top level – e.g., a much larger bid-size suggests buyers may soon lift the ask, pushing price up.

### Normalized Spread
Spread relative to mid-price (i.e., `(Ask–Bid)/MidPrice`).  

**What**: Scales the spread by price level, making it comparable across assets or times.  

**Why**: A fixed spread of 0.10 USD means very different liquidity when the mid-price is 10 vs 1000. This normalized spread is a dimensionless liquidity metric.

### Depth Concentration
A measure of how concentrated volume is at the top.  

**Calculation**:  
`Frac1 = Volume[1] / ∑_{i=1..N} Volume[i]`.  

**What**: High `Frac1` means most orders are at best price; low means a spread-out book.  

**Why useful**: If volume is highly concentrated at level 1, the book might be sensitive to any trade that exhausts that level (i.e., small trades move price). If volume is spread out, the book is deeper.

### Liquidity-Weighted Imbalance
Combine price and volume by e.g., taking a weighted price distance:  
`(VWAP_bid - VWAP_ask) / MidPrice`.  

**What**: This represents the cost difference of walking the book.  

**Why**: A large distance between bid and ask VWAP indicates one side is very thin. It’s another way to encode price impact.

---

### Summary
Each feature above is based on simple aggregates or differences of Kraken’s Level-2 data (top bids/asks and volumes), so it can be computed on the fly as updates arrive. By including a variety of these (e.g., spread, depth sums, volume imbalances, and weighted versions) in the PPO state, the agent gets rich signals about supply-demand imbalance and liquidity, which are known to correlate with short-term price moves.

## PPO (Proximal Policy Optimization) for Trading

PPO is an on-policy reinforcement learning algorithm that works well for continuous action spaces — exactly what you need for trading decisions. The order book features outlined would provide rich state observations that can help a PPO agent make more informed decisions.

### Why These Features Are Valuable for PPO

#### Market Microstructure Visibility
PPO needs a comprehensive state representation to make optimal decisions. These order book features (spreads, depths, imbalances) capture the market's microstructure — essentially giving your agent "X-ray vision" into supply and demand dynamics.

#### Forward-Looking Signals
Many of the features (like imbalance ratios and weighted book imbalances) are leading indicators of price movements. PPO can learn to recognize patterns in these features that precede market moves.

#### Multi-dimensional Input
PPO performs well with rich, multi-dimensional state spaces. The combination of price features, liquidity metrics, and imbalance indicators provides complementary information that helps the model understand market conditions more comprehensively.

#### Normalization
Many features are already normalized (like imbalance ratios between -1 and 1), which helps with PPO's learning stability.

#### Risk Management
Features like "Price Range/Book Width" help quantify potential slippage, allowing the PPO agent to learn about execution risk alongside directional opportunities.

### Key Insight
The **imbalance and pressure metrics** are particularly valuable for PPO, as they contain strong predictive signals about short-term price direction that a reinforcement learning agent could leverage very effectively.

---

### Suggested PPO State Representation
A minimal state vector for trading might include:
```python
state = [
    mid_price,                  # Current market equilibrium
    normalized_spread,          # Liquidity measure
    depth_imbalance_ratio,      # Buy/sell pressure (-1 to 1)
    vwap_ask - vwap_bid,        # Liquidity-weighted spread
    cumulative_volume_delta,    # Recent net volume momentum
    best_level_volume_ratio,    # Immediate supply/demand
    price_range_width           # Slippage risk metric
]